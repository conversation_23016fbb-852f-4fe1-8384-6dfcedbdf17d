<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\MetaTokenRequest;
use App\Models\MetaToken;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class MetaTokenController extends Controller
{
    use ApiResponseTrait;

    /**
     * Display a listing of meta tokens
     */
    public function index(Request $request): JsonResponse
    {
        $query = MetaToken::query();

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }

        // Filter by page ID
        if ($request->has('page_id')) {
            $query->where('page_id', $request->input('page_id'));
        }

        $tokens = $query->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 15));

        // Transform tokens to hide sensitive data
        $transformedTokens = $tokens->getCollection()->map(function ($token) {
            return [
                'id'           => $token->id,
                'page_id'      => $token->page_id,
                'page_name'    => $token->page_name,
                'expires_at'   => $token->expires_at,
                'status'       => $token->status,
                'scopes'       => $token->scopes,
                'masked_token' => $token->masked_token,
                'is_valid'     => $token->isValid(),
                'created_at'   => $token->created_at,
                'updated_at'   => $token->updated_at,
            ];
        });

        return $this->successResponse([
            'tokens'     => $transformedTokens,
            'pagination' => [
                'current_page' => $tokens->currentPage(),
                'last_page'    => $tokens->lastPage(),
                'per_page'     => $tokens->perPage(),
                'total'        => $tokens->total(),
            ],
        ]);
    }

    /**
     * Store a new meta token
     */
    public function store(MetaTokenRequest $request): JsonResponse
    {
        $validated = $request->validated();

        // Verify token with Meta API before storing
        $verification = $this->verifyTokenWithMeta($validated['access_token']);

        if (!$verification['valid']) {
            return $this->errorResponse($verification['error'], 422);
        }

        // Check if a token for this page already exists
        $existingToken = MetaToken::where('page_id', $verification['page_id'])
            ->where('status', 'active')
            ->first();

        if ($existingToken) {
            // Deactivate existing token
            $existingToken->update(['status' => 'revoked']);
        }

        $token = MetaToken::create([
            'access_token' => $validated['access_token'],
            'page_id'      => $verification['page_id'],
            'page_name'    => $verification['page_name'] ?? null,
            'expires_at'   => $verification['expires_at'] ?? null,
            'status'       => 'active',
            'scopes'       => $verification['scopes'] ?? null,
        ]);

        Log::info('Meta token stored successfully', ['page_id' => $token->page_id]);

        $response = $this->successResponse([
            'token'   => [
                'id'           => $token->id,
                'page_id'      => $token->page_id,
                'page_name'    => $token->page_name,
                'expires_at'   => $token->expires_at,
                'status'       => $token->status,
                'scopes'       => $token->scopes,
                'masked_token' => $token->masked_token,
                'is_valid'     => $token->isValid(),
                'created_at'   => $token->created_at,
            ],
            'message' => 'Meta token stored successfully',
        ]);

        return $response->setStatusCode(201);
    }

    /**
     * Display the specified meta token
     */
    public function show(int $id): JsonResponse
    {
        $token = MetaToken::find($id);

        if (!$token) {
            return $this->errorResponse('Meta token not found', 404);
        }

        return $this->successResponse([
            'token' => [
                'id'           => $token->id,
                'page_id'      => $token->page_id,
                'page_name'    => $token->page_name,
                'expires_at'   => $token->expires_at,
                'status'       => $token->status,
                'scopes'       => $token->scopes,
                'masked_token' => $token->masked_token,
                'is_valid'     => $token->isValid(),
                'created_at'   => $token->created_at,
                'updated_at'   => $token->updated_at,
            ],
        ]);
    }

    /**
     * Update the specified meta token
     */
    public function update(MetaTokenRequest $request, int $id): JsonResponse
    {
        $token = MetaToken::find($id);

        if (!$token) {
            return $this->errorResponse('Meta token not found', 404);
        }

        $validated = $request->validated();

        // If updating the access token, verify it first
        if (isset($validated['access_token']) && $validated['access_token'] !== $token->access_token) {
            $verification = $this->verifyTokenWithMeta($validated['access_token']);

            if (!$verification['valid']) {
                return $this->errorResponse($verification['error'], 422);
            }

            $validated['page_id']    = $verification['page_id'];
            $validated['page_name']  = $verification['page_name'] ?? null;
            $validated['expires_at'] = $verification['expires_at'] ?? null;
            $validated['scopes']     = $verification['scopes'] ?? null;
        }

        $token->update($validated);

        Log::info('Meta token updated successfully', ['id' => $token->id, 'page_id' => $token->page_id]);

        return $this->successResponse([
            'token'   => [
                'id'           => $token->id,
                'page_id'      => $token->page_id,
                'page_name'    => $token->page_name,
                'expires_at'   => $token->expires_at,
                'status'       => $token->status,
                'scopes'       => $token->scopes,
                'masked_token' => $token->masked_token,
                'is_valid'     => $token->isValid(),
                'updated_at'   => $token->updated_at,
            ],
            'message' => 'Meta token updated successfully',
        ]);
    }

    /**
     * Remove the specified meta token
     */
    public function destroy(int $id): JsonResponse
    {
        $token = MetaToken::find($id);

        if (!$token) {
            return $this->errorResponse('Meta token not found', 404);
        }

        Log::info('Meta token deleted', ['id' => $token->id, 'page_id' => $token->page_id]);

        $token->delete();

        return $this->successResponse([
            'message' => 'Meta token deleted successfully',
        ]);
    }

    /**
     * Verify a meta token with Meta API
     */
    public function verify(int $id): JsonResponse
    {
        $token = MetaToken::find($id);

        if (!$token) {
            return $this->errorResponse('Meta token not found', 404);
        }

        $verification = $this->verifyTokenWithMeta($token->access_token);

        if ($verification['valid']) {
            // Update token information if still valid
            $token->update([
                'status'     => 'active',
                'page_name'  => $verification['page_name'] ?? $token->page_name,
                'expires_at' => $verification['expires_at'] ?? $token->expires_at,
                'scopes'     => $verification['scopes'] ?? $token->scopes,
            ]);

            return $this->successResponse([
                'valid'   => true,
                'token'   => [
                    'id'         => $token->id,
                    'page_id'    => $token->page_id,
                    'page_name'  => $token->page_name,
                    'expires_at' => $token->expires_at,
                    'status'     => $token->status,
                    'scopes'     => $token->scopes,
                    'is_valid'   => $token->isValid(),
                ],
                'message' => 'Token is valid',
            ]);
        } else {
            // Mark token as expired or revoked
            $token->update(['status' => 'expired']);

            return $this->errorResponse('Token verification failed: ' . $verification['error'], 422);
        }
    }

    /**
     * Get current page information using the token
     */
    public function getCurrentPage(Request $request): JsonResponse
    {
        // Get the most recent active token
        $token = MetaToken::active()->latest()->first();

        if (!$token) {
            return $this->errorResponse('No active Meta token found', 404);
        }

        try {
            $response = Http::get(config('services.meta.graph_api_url') . '/me', [
                'access_token' => $token->access_token,
                'fields'       => 'id,name,username,followers_count,media_count',
            ]);

            if ($response->successful()) {
                $pageData = $response->json();

                return $this->successResponse([
                    'page'       => $pageData,
                    'token_info' => [
                        'id'         => $token->id,
                        'page_id'    => $token->page_id,
                        'status'     => $token->status,
                        'expires_at' => $token->expires_at,
                        'is_valid'   => $token->isValid(),
                    ],
                ]);
            } else {
                Log::error('Failed to get current page info', [
                    'token_id' => $token->id,
                    'response' => $response->json(),
                ]);

                return $this->errorResponse('Failed to retrieve page information', 422);
            }
        } catch (\Exception $e) {
            Log::error('Exception getting current page info', [
                'token_id' => $token->id,
                'error'    => $e->getMessage(),
            ]);

            return $this->errorResponse('Error retrieving page information', 500);
        }
    }

    /**
     * Verify token with Meta API
     */
    private function verifyTokenWithMeta(string $accessToken): array
    {
        try {
            $response = Http::get(config('services.meta.graph_api_url') . '/me', [
                'access_token' => $accessToken,
                'fields'       => 'id,name,username,access_token,scopes',
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // Check if token has required permissions
                $requiredScopes = ['pages_messaging', 'pages_show_list'];
                $tokenScopes    = $data['scopes'] ?? [];

                $missingScopes = array_diff($requiredScopes, $tokenScopes);
                if (!empty($missingScopes)) {
                    return [
                        'valid' => false,
                        'error' => 'Token missing required scopes: ' . implode(', ', $missingScopes),
                    ];
                }

                return [
                    'valid'      => true,
                    'page_id'    => $data['id'],
                    'page_name'  => $data['name'] ?? null,
                    'expires_at' => null, // Instagram tokens don't typically expire
                    'scopes'     => $tokenScopes,
                ];
            } else {
                $error = $response->json();
                return [
                    'valid' => false,
                    'error' => $error['error']['message'] ?? 'Token verification failed',
                ];
            }
        } catch (\Exception $e) {
            Log::error('Meta token verification failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'valid' => false,
                'error' => 'Unable to verify token with Meta API',
            ];
        }
    }
}