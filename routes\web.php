<?php

use App\Http\Controllers\Api\InstagramAuthController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/privacy-policy', function () {
    return view('privacy-policy');
})->name('privacy-policy');

// =============================================================================
// INSTAGRAM OAUTH ROUTES
// =============================================================================
Route::prefix('auth/instagram')->group(function () {
    Route::get('/', [InstagramAuthController::class, 'redirectToInstagram'])->name('instagram.auth');
    Route::get('/callback', [InstagramAuthController::class, 'handleCallback'])->name('instagram.callback');
    Route::get('/status', [InstagramAuthController::class, 'getConnectionStatus'])->name('instagram.status');
    Route::post('/disconnect', [InstagramAuthController::class, 'disconnect'])->name('instagram.disconnect');
    Route::post('/refresh', [InstagramAuthController::class, 'refreshToken'])->name('instagram.refresh');
});