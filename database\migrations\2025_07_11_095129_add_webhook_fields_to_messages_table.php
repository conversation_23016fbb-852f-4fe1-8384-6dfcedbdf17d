<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            // Add fields needed for webhook processing
            $table->string('message_id')->nullable()->after('id')->index();
            $table->string('recipient_id')->nullable()->after('sender_id');
            $table->enum('direction', ['incoming', 'outgoing'])->default('incoming')->after('platform');
            $table->timestamp('timestamp')->nullable()->after('sent_at');
            $table->foreignId('meta_token_id')->nullable()->after('meta_data')->constrained('meta_tokens')->onDelete('set null');
            $table->foreignId('in_reply_to')->nullable()->after('meta_token_id')->references('id')->on('messages')->onDelete('set null');

            // Rename content to message for consistency with webhook payload
            $table->renameColumn('content', 'message');

            // Add indexes for efficient queries
            $table->index(['recipient_id']);
            $table->index(['direction']);
            $table->index(['meta_token_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex(['recipient_id']);
            $table->dropIndex(['direction']);
            $table->dropIndex(['meta_token_id']);
            $table->dropIndex(['message_id']);

            // Drop foreign keys
            $table->dropForeign(['meta_token_id']);
            $table->dropForeign(['in_reply_to']);

            // Drop columns
            $table->dropColumn([
                'message_id',
                'recipient_id',
                'direction',
                'timestamp',
                'meta_token_id',
                'in_reply_to',
            ]);

            // Rename message back to content
            $table->renameColumn('message', 'content');
        });
    }
};
