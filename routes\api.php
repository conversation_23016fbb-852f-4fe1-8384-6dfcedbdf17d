<?php

declare (strict_types = 1);

use App\Http\Controllers\Api\BusinessConfigController;
use App\Http\Controllers\Api\FacebookWebhookController;
use App\Http\Controllers\Api\InstagramAuthController;
use App\Http\Controllers\Api\InstagramWebhookController;
use App\Http\Controllers\Api\IntegrationsController;
use App\Http\Controllers\Api\KnowledgeUploadController;
use App\Http\Controllers\Api\MetaTokenController;
use App\Http\Controllers\Api\SocialMediaController;
use App\Http\Controllers\Auth\AuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
 */

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status'    => 'ok',
        'timestamp' => now(),
        'service'   => 'Instagram Chatbot API',
    ]);
});

// =============================================================================
// SOCIAL MEDIA WEBHOOKS (Public)
// =============================================================================
Route::prefix('webhooks')->group(function () {
    // Instagram webhook
    Route::get('instagram', [InstagramWebhookController::class, 'verify']);
    Route::post('instagram', [InstagramWebhookController::class, 'webhook']);

    // Facebook webhook
    Route::get('facebook', [FacebookWebhookController::class, 'verify']);
    Route::post('facebook', [FacebookWebhookController::class, 'webhook']);
});

// =============================================================================
// INSTAGRAM OAUTH API (Public)
// =============================================================================
Route::prefix('auth/instagram')->group(function () {
    Route::get('/', [InstagramAuthController::class, 'redirectToInstagram']);
    Route::get('/callback', [InstagramAuthController::class, 'handleCallbackJson']);
    Route::get('/status', [InstagramAuthController::class, 'getConnectionStatus']);
    Route::post('/disconnect', [InstagramAuthController::class, 'disconnect']);
    Route::post('/refresh', [InstagramAuthController::class, 'refreshToken']);
});

// =============================================================================
// INTEGRATION CHANNELS
// =============================================================================
Route::prefix('integrations')->group(function () {
    // Public routes for integration status and auth URLs
    Route::get('status', [IntegrationsController::class, 'index']);

    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('channels', [BusinessConfigController::class, 'getIntegrationChannels']);
        Route::put('channels', [BusinessConfigController::class, 'updateIntegrationChannels']);
    });
});

// =============================================================================
// API v1 ENDPOINTS (Protected)
// =============================================================================
Route::prefix('v1')->group(function () {
    // =============================================================================
    // AUTH ROUTES
    // =============================================================================
    Route::prefix('auth')->group(function () {
        // Public auth routes
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
        Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('reset-password', [AuthController::class, 'resetPassword']);

        // Protected auth routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('logout', [AuthController::class, 'logout']);
            Route::get('user', [AuthController::class, 'user']);
            Route::post('otp-verify', [AuthController::class, 'otpVerify']);
            Route::post('resend-verification', [AuthController::class, 'resendVerification']);
        });
    });

    // Protected routes that require authentication
    Route::middleware('auth:sanctum')->group(function () {
        // =============================================================================
        // SOCIAL MEDIA MANAGEMENT
        // =============================================================================
        Route::prefix('social')->group(function () {
            // Send messages
            Route::post('send-message', [SocialMediaController::class, 'sendMessage']);

            // Platform-specific endpoints
            Route::prefix('{platform}')->where(['platform' => 'instagram|facebook'])->group(function () {
                Route::get('messages', [SocialMediaController::class, 'getMessages']);
                Route::get('stats', [SocialMediaController::class, 'getPlatformStats']);
                Route::get('conversations/history', [SocialMediaController::class, 'getConversationHistory']);
                Route::get('conversations/active', [SocialMediaController::class, 'getActiveConversations']);
                Route::get('messages/pending', [SocialMediaController::class, 'getPendingMessages']);
                Route::get('analytics/hourly', [SocialMediaController::class, 'getHourlyDistribution']);
            });
        });

        // =============================================================================
        // BUSINESS CONFIGURATION
        // =============================================================================
        Route::prefix('business')->group(function () {
            Route::get('configs', [BusinessConfigController::class, 'index']);
            Route::get('config/{key}', [BusinessConfigController::class, 'getConfig']);
            Route::put('config/{key}', [BusinessConfigController::class, 'updateConfig']);
            Route::get('config/category/{category}', [BusinessConfigController::class, 'getByCategory']);
            Route::get('info', [BusinessConfigController::class, 'getBusinessInfo']);
            Route::put('info', [BusinessConfigController::class, 'updateBusinessInfo']);
            Route::get('services', [BusinessConfigController::class, 'getServices']);
            Route::get('services/category/{category}', [BusinessConfigController::class, 'getServicesByCategory']);
            Route::put('services', [BusinessConfigController::class, 'updateServices']);
        });

        // =============================================================================
        // AGENT CONFIGURATION
        // =============================================================================
        Route::prefix('agent')->group(function () {
            Route::get('profile', [BusinessConfigController::class, 'getAgentProfile']);
            Route::put('profile', [BusinessConfigController::class, 'updateAgentProfile']);
        });

        // =============================================================================
        // SCHEDULING MANAGEMENT
        // =============================================================================
        Route::prefix('scheduling')->group(function () {
            // Time slots configuration
            Route::get('time-slots/config', [BusinessConfigController::class, 'getTimeSlotsConfig']);
            Route::put('time-slots/config', [BusinessConfigController::class, 'updateTimeSlotsConfig']);

            // Available slots
            Route::get('slots/available', [BusinessConfigController::class, 'getAvailableSlots']);
            Route::get('slots/next-available', [BusinessConfigController::class, 'getNextAvailableSlot']);

            // Weekend settings
            Route::get('weekend/settings', [BusinessConfigController::class, 'getWeekendSettings']);
            Route::put('weekend/settings', [BusinessConfigController::class, 'updateWeekendSettings']);

            // Unavailable dates
            Route::get('unavailable-dates', [BusinessConfigController::class, 'getUnavailableDates']);
            Route::post('unavailable-dates', [BusinessConfigController::class, 'addUnavailableDate']);
            Route::delete('unavailable-dates', [BusinessConfigController::class, 'removeUnavailableDate']);

            // Disabled time slots
            Route::get('disabled-slots', [BusinessConfigController::class, 'getDisabledTimeSlots']);
            Route::post('disabled-slots', [BusinessConfigController::class, 'disableTimeSlot']);
            Route::delete('disabled-slots', [BusinessConfigController::class, 'enableTimeSlot']);
        });

        // =============================================================================
        // AI CHATBOT
        // =============================================================================
        Route::prefix('chatbot')->group(function () {
            Route::get('prompts', [BusinessConfigController::class, 'getChatbotPrompts']);
            Route::put('prompts', [BusinessConfigController::class, 'updateChatbotPrompts']);
        });

        // =============================================================================
        // KNOWLEDGE BASE
        // =============================================================================
        Route::prefix('knowledge')->group(function () {
            Route::post('upload', [KnowledgeUploadController::class, 'upload']);
            Route::get('files', [KnowledgeUploadController::class, 'index']);
            Route::get('files/{id}', [KnowledgeUploadController::class, 'show']);
            Route::delete('files/{id}', [KnowledgeUploadController::class, 'destroy']);
            Route::post('search', [KnowledgeUploadController::class, 'search']);
            Route::get('stats', [KnowledgeUploadController::class, 'stats']);
            Route::post('files/{id}/reprocess', [KnowledgeUploadController::class, 'reprocess']);
        });

        // =============================================================================
        // META TOKEN MANAGEMENT
        // =============================================================================
        Route::prefix('meta')->group(function () {
            Route::get('tokens', [MetaTokenController::class, 'index']);
            Route::post('tokens', [MetaTokenController::class, 'store']);
            Route::get('tokens/{id}', [MetaTokenController::class, 'show']);
            Route::put('tokens/{id}', [MetaTokenController::class, 'update']);
            Route::delete('tokens/{id}', [MetaTokenController::class, 'destroy']);
            Route::post('tokens/{id}/verify', [MetaTokenController::class, 'verify']);
            Route::get('current-page', [MetaTokenController::class, 'getCurrentPage']);
        });
    });
});

// =============================================================================
// FALLBACK ROUTE
// =============================================================================
Route::fallback(function () {
    return response()->json([
        'message'             => 'API endpoint not found',
        'available_endpoints' => [
            'social_media'    => [
                'send_message'         => 'POST /api/v1/social/send-message',
                'platform_stats'       => 'GET /api/v1/social/{platform}/stats',
                'platform_messages'    => 'GET /api/v1/social/{platform}/messages',
                'conversation_history' => 'GET /api/v1/social/{platform}/conversations/history',
            ],
            'webhooks'        => [
                'instagram' => 'POST /api/webhooks/instagram',
                'facebook'  => 'POST /api/webhooks/facebook',
            ],
            'business_config' => [
                'agent_profile'        => 'GET/PUT /api/v1/agent/profile',
                'business_info'        => 'GET/PUT /api/v1/business/info',
                'integration_channels' => 'GET/PUT /api/v1/integrations/channels',
            ],
            'chatbot'         => [
                'prompts' => 'GET/PUT /api/v1/chatbot/prompts',
            ],
            'auth'            => [
                'register'        => 'POST /api/v1/auth/register',
                'login'           => 'POST /api/v1/auth/login',
                'forgot_password' => 'POST /api/v1/auth/forgot-password',
                'reset_password'  => 'POST /api/v1/auth/reset-password',
            ],
        ],
    ], 404);
});