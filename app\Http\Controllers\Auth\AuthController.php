<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\OtpVerificationRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Resources\AuthResource;
use App\Services\Auth\AuthService;
use App\Traits\ApiResponseTrait;

class AuthController extends Controller
{
    use ApiResponseTrait;

    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function register(RegisterRequest $request)
    {
        try {
            $user = $this->authService->register($request->validated());

            return $this->successResponse(
                new AuthResource($user['user'], $user['token']),
                'User registered successfully. Please check your email for verification OTP.'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    public function login(LoginRequest $request)
    {
        try {
            $result = $this->authService->login(
                $request->email,
                $request->password,
                ['remember' => $request->remember]
            );

            return $this->successResponse(
                new AuthResource($result['user'], $result['token']),
                'Login successful'
            );

        } catch (\Exception $e) {
            $code = $e->getCode() ?: 401;
            return $this->errorResponse(
                $e->getMessage(),
                $code,
                ['error' => $e->getMessage()]
            );
        }
    }

    public function otpVerify(OtpVerificationRequest $request)
    {
        try {
            $user = $this->authService->otpVerify(
                auth()->id(),
                $request->otp
            );

            return $this->successResponse(
                new AuthResource($user),
                'Email verified successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                400,
                ['error' => $e->getMessage()]
            );
        }
    }

    public function logout()
    {
        try {
            auth()->user()->currentAccessToken()->delete();

            return $this->successResponse(
                null,
                'Successfully logged out'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    public function user()
    {
        try {
            return $this->successResponse(
                new AuthResource(auth()->user()),
                'User details retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    public function forgotPassword(ForgotPasswordRequest $request)
    {
        try {
            $this->authService->forgotPassword($request->email);

            return $this->successResponse(
                null,
                'Password reset link sent to your email'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        try {
            $this->authService->resetPassword(
                $request->email,
                $request->otp,
                $request->password
            );

            return $this->successResponse(
                null,
                'Password reset successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                400,
                ['error' => $e->getMessage()]
            );
        }
    }

    public function resendVerification()
    {
        try {
            $this->authService->resendVerification(auth()->id());

            return $this->successResponse(
                null,
                'Verification email sent successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
}
