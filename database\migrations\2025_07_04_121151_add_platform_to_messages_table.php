<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            $table->string('platform')->default('instagram')->after('conversation_id');
            $table->index(['platform', 'status']);
            $table->index(['platform', 'sender_id']);
            $table->index(['platform', 'conversation_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            $table->dropIndex(['platform', 'status']);
            $table->dropIndex(['platform', 'sender_id']);
            $table->dropIndex(['platform', 'conversation_id']);
            $table->dropColumn('platform');
        });
    }
};
