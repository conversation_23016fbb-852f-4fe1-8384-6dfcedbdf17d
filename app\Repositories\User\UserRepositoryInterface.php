<?php

declare (strict_types = 1);

namespace App\Repositories\User;

interface UserRepositoryInterface
{
    public function create(array $data);
    public function findByEmail(string $email);
    public function verifyEmail(int $userId);
    public function updatePassword(string $email, string $password);
    public function storeResetToken(string $email, string $token);
    public function findResetToken(string $email);
    public function deleteResetToken(string $email);
    public function findById(int $userId);
}