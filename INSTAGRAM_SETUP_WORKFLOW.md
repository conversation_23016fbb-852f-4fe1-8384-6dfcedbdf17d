# Instagram API Setup & Authentication Workflow

## Overview

This guide walks you through setting up Instagram API access for your Kortana AI chatbot application, from initial Facebook/Meta developer setup to testing your endpoints.

## Prerequisites

-   Laravel application running on `http://kortana-ai.test`
-   Facebook/Meta Developer Account
-   Instagram Business Account
-   Facebook Page connected to Instagram Business Account

## Step 1: Meta Developer Setup

### 1.1 Create Facebook App

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click **"Create App"**
3. Select **"Business"** as app type
4. Fill in app details:
    - **App Name**: `Kortana AI Chatbot`
    - **App Contact Email**: Your email
    - **Business Account**: Select or create one

### 1.2 Add Instagram Basic Display Product

1. In your app dashboard, click **"Add Product"**
2. Find **"Instagram Basic Display"** and click **"Set Up"**
3. Go to **Instagram Basic Display > Basic Display**
4. Add Instagram Test Users (your Instagram account)

### 1.3 Add Messenger Product (for Instagram messaging)

1. Click **"Add Product"** again
2. Find **"Messenger"** and click **"Set Up"**
3. This enables Instagram messaging capabilities

## Step 2: Configure Instagram Business API

### 2.1 Get Page Access Token

1. Go to **Graph API Explorer**: https://developers.facebook.com/tools/explorer/
2. Select your app from dropdown
3. Click **"Generate Access Token"**
4. Grant permissions:
    - `pages_show_list`
    - `pages_read_engagement`
    - `pages_manage_metadata`
    - `instagram_basic`
    - `instagram_manage_messages`
    - `instagram_manage_comments`

### 2.2 Get Long-Lived Access Token

```bash
# Exchange short-lived token for long-lived token
curl -X GET "https://graph.facebook.com/v23.0/oauth/access_token" \
  -d "grant_type=fb_exchange_token" \
  -d "client_id=YOUR_APP_ID" \
  -d "client_secret=YOUR_APP_SECRET" \
  -d "fb_exchange_token=YOUR_SHORT_LIVED_TOKEN"
```

### 2.3 Get Instagram Business Account ID

```bash
# Get your Facebook Pages
curl -X GET "https://graph.facebook.com/v23.0/me/accounts" \
  -d "access_token=YOUR_LONG_LIVED_TOKEN"

# Get Instagram Business Account connected to your page
curl -X GET "https://graph.facebook.com/v23.0/PAGE_ID" \
  -d "fields=instagram_business_account" \
  -d "access_token=YOUR_LONG_LIVED_TOKEN"
```

## Step 3: Laravel Environment Configuration

### 3.1 Update .env File

```env
# Meta/Facebook Configuration
META_APP_ID=your_facebook_app_id
META_APP_SECRET=your_facebook_app_secret
META_WEBHOOK_VERIFY_TOKEN=your_custom_verify_token
META_PAGE_ACCESS_TOKEN=your_long_lived_page_access_token

# Instagram Configuration
INSTAGRAM_BUSINESS_ACCOUNT_ID=your_instagram_business_account_id
INSTAGRAM_PAGE_ID=your_facebook_page_id

# Webhook URLs
INSTAGRAM_WEBHOOK_URL=http://kortana-ai.test/api/webhooks/instagram
FACEBOOK_WEBHOOK_URL=http://kortana-ai.test/api/webhooks/facebook

# App Configuration
APP_URL=http://kortana-ai.test
```

### 3.2 Add Configuration to config/services.php

```php
<?php

return [
    // ... existing services

    'meta' => [
        'app_id' => env('META_APP_ID'),
        'app_secret' => env('META_APP_SECRET'),
        'webhook_verify_token' => env('META_WEBHOOK_VERIFY_TOKEN'),
        'page_access_token' => env('META_PAGE_ACCESS_TOKEN'),
    ],

    'instagram' => [
        'business_account_id' => env('INSTAGRAM_BUSINESS_ACCOUNT_ID'),
        'page_id' => env('INSTAGRAM_PAGE_ID'),
        'webhook_url' => env('INSTAGRAM_WEBHOOK_URL'),
    ],

    'facebook' => [
        'webhook_url' => env('FACEBOOK_WEBHOOK_URL'),
    ],
];
```

## Step 4: Webhook Configuration

### 4.1 Setup Webhook URL in Meta Developer Console

1. Go to your Facebook App Dashboard
2. Navigate to **Messenger > Settings**
3. In **Webhooks** section, click **"Add Callback URL"**
4. Enter webhook details:
    - **Callback URL**: `http://kortana-ai.test/api/webhooks/instagram`
    - **Verify Token**: Your custom verify token from .env
5. Subscribe to webhook fields:
    - `messages`
    - `messaging_postbacks`
    - `message_deliveries`
    - `message_reads`

### 4.2 Test Webhook Verification

```bash
# Test webhook verification endpoint
curl -X GET "http://kortana-ai.test/api/webhooks/instagram" \
  -d "hub.mode=subscribe" \
  -d "hub.challenge=test_challenge_123" \
  -d "hub.verify_token=your_verify_token"

# Should return: test_challenge_123
```

## Step 5: Authentication Setup

### 5.1 Create Meta Token in Database

Use Postman or curl to create meta token configuration:

```json
POST http://kortana-ai.test/api/v1/meta-token
Authorization: Bearer YOUR_AUTH_TOKEN
Content-Type: application/json

{
  "access_token": "your_long_lived_page_access_token",
  "page_id": "your_facebook_page_id",
  "app_id": "your_facebook_app_id",
  "app_secret": "your_facebook_app_secret",
  "webhook_verify_token": "your_webhook_verify_token"
}
```

### 5.2 Create Integration Record

```json
POST http://kortana-ai.test/api/v1/integrations
Authorization: Bearer YOUR_AUTH_TOKEN
Content-Type: application/json

{
  "platform": "instagram",
  "access_token": "your_long_lived_page_access_token",
  "page_id": "your_instagram_business_account_id",
  "webhook_url": "http://kortana-ai.test/api/webhooks/instagram",
  "is_active": true
}
```

## Step 6: Testing Workflow

### 6.1 Test Webhook Endpoints

```bash
# 1. Test Instagram webhook verification
curl -X GET "http://kortana-ai.test/api/webhooks/instagram?hub.mode=subscribe&hub.challenge=test123&hub.verify_token=your_verify_token"

# 2. Test Facebook webhook verification
curl -X GET "http://kortana-ai.test/api/webhooks/facebook?hub.mode=subscribe&hub.challenge=test123&hub.verify_token=your_verify_token"
```

### 6.2 Test Message Sending

```json
POST http://kortana-ai.test/api/v1/social/instagram/message
Authorization: Bearer YOUR_AUTH_TOKEN
Content-Type: application/json

{
  "recipient_id": "instagram_user_id",
  "message": "Hello! This is a test message from Kortana AI.",
  "message_type": "text"
}
```

### 6.3 Test Platform Statistics

```bash
GET http://kortana-ai.test/api/v1/social/stats?platform=instagram
Authorization: Bearer YOUR_AUTH_TOKEN
```

## Step 7: Live Testing

### 7.1 Simulate Incoming Message

Send a direct message to your Instagram Business account and check if the webhook receives it.

### 7.2 Monitor Logs

```bash
# Monitor Laravel logs for webhook activity
tail -f storage/logs/laravel.log

# Check for webhook payload
grep "Instagram webhook" storage/logs/laravel.log
```

### 7.3 Test Complete Flow

1. Send DM to your Instagram Business account
2. Verify webhook receives the message
3. Check if automatic response is sent
4. Verify message is stored in database

## Step 8: Production Considerations

### 8.1 HTTPS Requirements

-   Meta requires HTTPS for production webhooks
-   Use ngrok for local testing: `ngrok http 80 --host-header=kortana-ai.test`
-   Update webhook URL to ngrok HTTPS URL

### 8.2 Rate Limiting

-   Instagram API has rate limits
-   Implement queue jobs for message sending
-   Add retry logic for failed requests

### 8.3 Error Handling

-   Monitor webhook failures
-   Implement proper logging
-   Set up alerts for API errors

## Troubleshooting

### Common Issues

1. **Webhook Verification Failed**

    - Check verify token matches in .env and Meta console
    - Ensure endpoint returns exact challenge string

2. **Access Token Invalid**

    - Regenerate long-lived token
    - Check token permissions
    - Verify page is connected to Instagram Business account

3. **Message Sending Failed**

    - Verify recipient has messaged your page first
    - Check Instagram Business account ID
    - Ensure proper permissions granted

4. **Webhook Not Receiving Messages**
    - Verify webhook subscription in Meta console
    - Check if HTTPS is required
    - Test with ngrok tunnel

### Debug Commands

```bash
# Test API connectivity
curl -X GET "https://graph.facebook.com/v23.0/me?access_token=YOUR_TOKEN"

# Check Instagram account details
curl -X GET "https://graph.facebook.com/v23.0/INSTAGRAM_ACCOUNT_ID?fields=id,username&access_token=YOUR_TOKEN"

# Test message sending directly via Graph API
curl -X POST "https://graph.facebook.com/v23.0/INSTAGRAM_ACCOUNT_ID/messages" \
  -d "recipient={\"id\":\"USER_ID\"}" \
  -d "message={\"text\":\"Test message\"}" \
  -d "access_token=YOUR_TOKEN"
```

## Next Steps

1. Set up Facebook integration following similar process
2. Implement Twitter API integration
3. Add message templates and quick replies
4. Set up automated responses based on keywords
5. Implement conversation flow management
6. Add analytics and reporting features

## Security Notes

-   Never commit access tokens to version control
-   Rotate tokens regularly
-   Use environment variables for all sensitive data
-   Implement proper input validation
-   Monitor for suspicious webhook activity
-   Set up proper CORS policies

---

**Need Help?**

-   Check Meta Developer Documentation: https://developers.facebook.com/docs/instagram-api
-   Instagram Messaging API: https://developers.facebook.com/docs/messenger-platform/instagram
-   Laravel Documentation: https://laravel.com/docs
