<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

final class MetaToken extends Model
{
    use HasFactory;

    protected $fillable = [
        'platform',
        'app_scope_id',
        'user_id',
        'username',
        'name',
        'account_type',
        'profile_picture_url',
        'followers_count',
        'follows_count',
        'media_count',
        'access_token',
        'token_type',
        'expires_at',
        'status',
        'is_active',
        'scopes',
        'has_business_permission',
        'page_id',
    ];

    protected $casts = [
        'expires_at'              => 'datetime',
        'scopes'                  => 'array',
        'status'                  => 'string',
        'is_active'               => 'boolean',
        'has_business_permission' => 'boolean',
        'media_count'             => 'integer',
        'followers_count'         => 'integer',
        'follows_count'           => 'integer',
    ];

    protected $hidden = [
        'access_token',
    ];

    public function getRouteKeyName(): string
    {
        return 'id';
    }

    /**
     * Scope to get active tokens
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            });
    }

    /**
     * Scope to get tokens for a specific platform
     */
    public function scopeForPlatform($query, string $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * Scope to get tokens for a specific page (Facebook)
     */
    public function scopeForPage($query, string $pageId)
    {
        return $query->where('page_id', $pageId);
    }

    /**
     * Scope to get tokens for a specific Instagram account
     */
    public function scopeForInstagramAccount($query, string $accountId)
    {
        return $query->where('platform', 'instagram')
            ->where(function ($q) use ($accountId) {
                $q->where('app_scope_id', $accountId)
                    ->orWhere('user_id', $accountId);
            });
    }

    /**
     * Scope to get tokens with business permissions
     */
    public function scopeWithBusinessPermission($query)
    {
        return $query->where('has_business_permission', true);
    }

    /**
     * Check if token is expired
     */
    public function isExpired(): bool
    {
        if ($this->expires_at === null) {
            return false;
        }

        return $this->expires_at->isPast();
    }

    /**
     * Check if token is active and not expired
     */
    public function isValid(): bool
    {
        return $this->is_active && !$this->isExpired();
    }

    /**
     * Check if this is an Instagram token
     */
    public function isInstagramToken(): bool
    {
        return $this->platform === 'instagram';
    }

    /**
     * Check if this is a Facebook token
     */
    public function isFacebookToken(): bool
    {
        return $this->platform === 'facebook';
    }

    /**
     * Check if token has business permissions
     */
    public function hasBusinessPermission(): bool
    {
        return $this->has_business_permission;
    }

    /**
     * Get masked token for display
     */
    public function getMaskedTokenAttribute(): string
    {
        $token = $this->access_token;
        if (strlen($token) <= 8) {
            return str_repeat('*', strlen($token));
        }

        return substr($token, 0, 4) . str_repeat('*', strlen($token) - 8) . substr($token, -4);
    }

    /**
     * Get the display name for the token (name for Instagram, name or default for Facebook)
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->isInstagramToken()) {
            return $this->name ?? $this->username ?? 'Instagram Account';
        }

        return $this->name ?? 'Facebook Page';
    }

    /**
     * Get the account identifier (user_id for Instagram, page_id for Facebook)
     */
    public function getAccountIdAttribute(): string
    {
        if ($this->isInstagramToken()) {
            return $this->user_id ?? $this->app_scope_id ?? '';
        }

        return $this->page_id ?? '';
    }

    /**
     * Check if token is eligible for refresh
     * Tokens must be at least 24 hours old and not expired
     */
    public function isEligibleForRefresh(): bool
    {
        return $this->isValid()
        && $this->updated_at->diffInHours(now()) >= 24
        && $this->hasBusinessPermission();
    }

    /**
     * Check if token needs refreshing (within 7 days of expiration)
     */
    public function needsRefresh(): bool
    {
        if (!$this->expires_at) {
            return false;
        }

        return $this->expires_at->subDays(7)->isPast() && $this->isValid();
    }

    /**
     * Get days until expiration
     */
    public function getDaysUntilExpirationAttribute(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }

        if ($this->isExpired()) {
            return 0;
        }

        return (int) now()->diffInDays($this->expires_at);
    }
}
