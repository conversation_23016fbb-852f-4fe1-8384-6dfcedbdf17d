<?php

declare (strict_types = 1);

namespace App\Services;

use App\Models\Appointment;
use App\Models\BusinessConfig;
use App\Models\Message;
use App\Repositories\KnowledgeRepository;
use App\Repositories\MessageRepository;
use Carbon\Carbon;
use Exception;
use GeminiAPI\Client as GeminiClient;
use GeminiAPI\GenerationConfig;
use GeminiAPI\Resources\ModelName;
use GeminiAPI\Resources\Parts\TextPart;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class ChatbotService
{
    private const META_GRAPH_URL = 'https://graph.facebook.com/v23.0';

    private GeminiClient $geminiClient;
    private ?OllamaService $ollamaService;
    private bool $useOllama;

    public function __construct(
        private readonly MessageRepository $messageRepository,
        private readonly KnowledgeRepository $knowledgeRepository,
        private readonly InstagramService $instagramService
    ) {
        $this->geminiClient = new GeminiClient(config('services.gemini.api_key'));

        // Initialize Ollama service if enabled
        $this->useOllama     = config('services.ollama.enabled', false);
        $this->ollamaService = $this->useOllama ? app(OllamaService::class) : null;

        // Check if Gemini is enabled via env variable
        $geminiEnabled = config('services.gemini.enabled', true);

        // Check if Ollama is actually available
        if ($this->useOllama && $this->ollamaService) {
            try {
                $this->useOllama = $this->ollamaService->isAvailable();
                if (!$this->useOllama) {
                    if (!$geminiEnabled) {
                        Log::error('Ollama service is not available and Gemini is not enabled. The application will not function correctly.');
                    } else {
                        Log::warning('Ollama service is enabled but not available. Falling back to Gemini.');
                    }
                }
            } catch (Exception $e) {
                $this->useOllama = false;
                if (!$geminiEnabled) {
                    Log::error('Failed to connect to Ollama service and Gemini is not enabled. The application will not function correctly.', [
                        'error' => $e->getMessage(),
                    ]);
                } else {
                    Log::warning('Failed to connect to Ollama service. Falling back to Gemini.', [
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        // Force use of Ollama if Gemini is not enabled
        if (!$geminiEnabled) {
            $this->useOllama = true;
            if (!$this->ollamaService || !$this->ollamaService->isAvailable()) {
                Log::error('Gemini is not enabled but Ollama is not available. AI responses will not work.');
            }
        }
    }

    /**
     * Process incoming Instagram message and generate AI response
     */
    public function processMessage(array $messageData): Message
    {
        try {
            // Rate limiting check
            $this->checkRateLimit($messageData['sender_id']);

            // Create message record
            $message = $this->messageRepository->create([
                'sender_id'       => $messageData['sender_id'],
                'conversation_id' => $messageData['conversation_id'] ?? $messageData['sender_id'],
                'message'         => $messageData['content'], // Changed from 'content' to 'message'
                'status'          => 'pending',
                'sent_at'         => now(),
                'meta_data'       => array_merge($messageData['meta_data'] ?? [], [
                    'recipient_id' => $messageData['recipient_id'] ?? null,
                ]),
            ]);

            // Mark as typing for user experience
            $this->messageRepository->markAsTyping($message);
            $this->sendTypingIndicator($message->sender_id, $messageData['recipient_id'] ?? null);

            // Generate and send AI response
            $response = $this->generateAIResponse($message);
            $this->sendMessage($message->sender_id, $response, $messageData['recipient_id'] ?? null);

            // Update message with reply
            return $this->messageRepository->markAsReplied($message, $response);

        } catch (Exception $e) {
            Log::error('Failed to process message', [
                'error'        => $e->getMessage(),
                'message_data' => $messageData,
            ]);

            throw $e;
        }
    }

    /**
     * Generate AI response using Gemini with conversation context and knowledge base
     */
    private function generateAIResponse(Message $message): string
    {
        try {
            // Check if this is an appointment-related conversation
            $appointmentIntent = $this->detectAppointmentIntent($message->message);

            // Get conversation context
            $conversationHistory = $this->getConversationContext($message->conversation_id);

            // Get relevant knowledge from vector search
            $relevantKnowledge = $this->findRelevantKnowledge($message->message);

            // Build prompt with context and business information
            $prompt = $this->buildPrompt($message->message, $conversationHistory, $relevantKnowledge, $appointmentIntent);

            // Generate response based on available services
            $aiResponse = $this->useOllama
            ? $this->generateOllamaResponse($prompt, $conversationHistory)
            : $this->generateGeminiResponse($prompt);
            // $aiResponse = $this->generateGeminiResponse($prompt);

            // Handle appointment booking if detected
            if ($appointmentIntent['detected']) {
                $aiResponse = $this->handleAppointmentBooking($message, $aiResponse, $appointmentIntent);
            }

            // Ensure response isn't too long even after appointment handling
            $aiResponse = $this->ensureResponseLength($aiResponse);

            return $aiResponse;

        } catch (Exception $e) {
            Log::error('Failed to generate AI response', [
                'error'      => $e->getMessage(),
                'message_id' => $message->id,
            ]);

            return BusinessConfig::getChatbotPrompt('error_response',
                'I apologize, but I\'m experiencing technical difficulties. Please try again in a moment.');
        }
    }

    /**
     * Generate response using Gemini API
     */
    private function generateGeminiResponse(string $prompt): string
    {
        // Configure Gemini generation with shorter responses for Instagram
        $config = (new GenerationConfig())
            ->withTemperature((float) config('services.gemini.temperature', 0.7))
            ->withTopP((float) config('services.gemini.top_p', 0.9))
            ->withTopK((int) config('services.gemini.top_k', 40))
            ->withMaxOutputTokens((int) config('services.gemini.max_tokens', 600)); // Reduced from 1000 to 600

        // Call Gemini API
        $modelName = config('services.gemini.model', 'gemini-2.0-flash-exp');
        $response  = $this->geminiClient->generativeModel($modelName)
            ->withGenerationConfig($config)
            ->generateContent(new TextPart($prompt));

        return $response->text() ?: 'I apologize, but I encountered an issue generating a response. Please try again.';
    }

    /**
     * Generate response using Ollama API
     */
    private function generateOllamaResponse(string $prompt, array $conversationHistory): string
    {
        try {
            // Format conversation history for Ollama
            $messages = [];

            // Add system message
            $messages[] = [
                'role'    => 'system',
                'content' => "You are a friendly AI assistant for our car care business. " .
                "You help customers with service information, pricing, and appointment booking. " .
                "Be conversational, helpful, and professional. " .
                "IMPORTANT: Keep responses short and concise for Instagram messaging (under 600 characters when possible). " .
                "Break complex information into simple, digestible parts. Use emojis sparingly and focus on the most relevant information.",
            ];

            // Add conversation history
            foreach ($conversationHistory as $msg) {
                $messages[] = [
                    'role'    => $msg['role'],
                    'content' => $msg['content'],
                ];
            }

            // Add the current prompt as the final user message if not already included
            if (empty($conversationHistory) || $conversationHistory[count($conversationHistory) - 1]['role'] !== 'user') {
                $messages[] = [
                    'role'    => 'user',
                    'content' => $prompt,
                ];
            }

            // Generate response using Ollama
            return $this->ollamaService->generateChatCompletion($messages);

        } catch (Exception $e) {
            Log::error('Failed to generate Ollama response, falling back to Gemini', [
                'error' => $e->getMessage(),
            ]);

            // Fallback to Gemini if Ollama fails
            return $this->generateGeminiResponse($prompt);
        }
    }

    /**
     * Detect appointment booking intent in user message
     */
    private function detectAppointmentIntent(string $message): array
    {

        $message = strtolower($message);

        $appointmentKeywords = [
            'appointment', 'book', 'schedule', 'reserve', 'availability',
            'available', 'time', 'slot', 'when', 'tomorrow', 'today',
            'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday',
        ];

        $serviceKeywords = array_keys(BusinessConfig::getServices());

        $detected         = false;
        $confidence       = 0;
        $detectedServices = [];
        $detectedTimes    = [];

        // Check for appointment keywords
        foreach ($appointmentKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                $detected = true;
                $confidence += 0.2;
            }
        }

        // Check for service mentions
        foreach ($serviceKeywords as $service) {
            if (strpos($message, str_replace('_', ' ', $service)) !== false) {
                $detectedServices[] = $service;
                $confidence += 0.3;
            }
        }

        // Check for time/date patterns
        if (preg_match('/\b(\d{1,2}):?(\d{2})?\s*(am|pm)\b/i', $message)) {
            $detected = true;
            $confidence += 0.4;
        }

        return [
            'detected'   => $detected,
            'confidence' => min($confidence, 1.0),
            'services'   => $detectedServices,
            'times'      => $detectedTimes,
        ];
    }

    /**
     * Handle appointment booking conversation flow
     */
    private function handleAppointmentBooking(Message $message, string $aiResponse, array $intent): string
    {
        try {
            // Get available slots for the next 7 days
            $availableSlots = BusinessConfig::getNextAvailableSlots(7);

            if (empty($availableSlots)) {
                return $aiResponse . "\n\nI'm sorry, but we don't have any available appointments in the next week. Please contact us directly for scheduling options.";
            }

            // Add available slots to response (limit for Instagram)
            $slotText  = "\n\n📅 **Available Slots:**\n";
            $slotCount = 0;

            foreach ($availableSlots as $daySlots) {
                if ($slotCount >= 2) {
                    break;
                }
                // Show only next 2 days to keep message shorter

                $slotText .= "\n**{$daySlots['formatted_date']}:**\n";
                foreach (array_slice($daySlots['slots'], 0, 2) as $slot) { // Show only first 2 slots per day
                    $slotText .= "• {$slot['formatted']}\n";
                }
                $slotCount++;
            }

            $slotText .= "\nTell me your preferred time to book!";

            // Try to book if specific time is mentioned
            $bookedAppointment = $this->attemptAutomaticBooking($message, $intent);
            if ($bookedAppointment) {
                return $this->generateBookingConfirmation($bookedAppointment);
            }

            return $aiResponse . $slotText;

        } catch (Exception $e) {
            Log::error('Failed to handle appointment booking', [
                'error'      => $e->getMessage(),
                'message_id' => $message->id,
            ]);

            return $aiResponse . "\n\nI'd be happy to help you schedule an appointment! Please let me know your preferred date and time.";
        }
    }

    /**
     * Attempt to automatically book appointment from message content
     */
    private function attemptAutomaticBooking(Message $message, array $intent): ?Appointment
    {
        // Extract date and time from message using regex patterns
        $content       = $message->message;
        $extractedDate = $this->extractDateFromMessage($content);
        $extractedTime = $this->extractTimeFromMessage($content);

        if (!$extractedDate || !$extractedTime) {
            return null;
        }

        try {
            $appointmentDateTime = Carbon::createFromFormat('Y-m-d H:i', $extractedDate . ' ' . $extractedTime);

            // Check if slot is available
            if (!BusinessConfig::isSlotAvailable($appointmentDateTime)) {
                return null;
            }

            // Create appointment
            $appointment = Appointment::create([
                'customer_name'    => 'Instagram User', // Default name
                'customer_id'      => $message->sender_id,
                'appointment_date' => $extractedDate,
                'time_slot'        => $extractedTime . ':00',
                'status'           => 'booked',
                'notes'            => 'Booked via Instagram chatbot',
                'contact_info'     => json_encode(['instagram_id' => $message->sender_id]),
            ]);

            Log::info('Automatic appointment booking successful', [
                'appointment_id' => $appointment->id,
                'customer_id'    => $message->sender_id,
                'datetime'       => $appointmentDateTime,
            ]);

            return $appointment;

        } catch (Exception $e) {
            Log::warning('Automatic appointment booking failed', [
                'error'          => $e->getMessage(),
                'extracted_date' => $extractedDate,
                'extracted_time' => $extractedTime,
            ]);

            return null;
        }
    }

    /**
     * Extract date from message content
     */
    private function extractDateFromMessage(string $content): ?string
    {
        $content = strtolower($content);

        // Check for relative dates
        if (strpos($content, 'today') !== false) {
            return Carbon::today()->format('Y-m-d');
        }

        if (strpos($content, 'tomorrow') !== false) {
            return Carbon::tomorrow()->format('Y-m-d');
        }

        // Check for day names
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        foreach ($days as $day) {
            if (strpos($content, $day) !== false) {
                return Carbon::parse('next ' . $day)->format('Y-m-d');
            }
        }

        // Check for date patterns (MM/DD, MM-DD, etc.)
        if (preg_match('/(\d{1,2})[\/\-](\d{1,2})/', $content, $matches)) {
            try {
                $month = (int) $matches[1];
                $day   = (int) $matches[2];
                $year  = Carbon::now()->year;

                $date = Carbon::create($year, $month, $day);
                if ($date->isPast()) {
                    $date->addYear();
                }

                return $date->format('Y-m-d');
            } catch (Exception $e) {
                return null;
            }
        }

        return null;
    }

    /**
     * Extract time from message content
     */
    private function extractTimeFromMessage(string $content): ?string
    {
        // Look for time patterns like "2:30 PM", "14:30", "2 PM", etc.
        if (preg_match('/(\d{1,2}):?(\d{2})?\s*(am|pm)/i', $content, $matches)) {
            $hour   = (int) $matches[1];
            $minute = isset($matches[2]) ? (int) $matches[2] : 0;
            $ampm   = strtolower($matches[3]);

            if ($ampm === 'pm' && $hour !== 12) {
                $hour += 12;
            } elseif ($ampm === 'am' && $hour === 12) {
                $hour = 0;
            }

            return sprintf('%02d:%02d', $hour, $minute);
        }

        return null;
    }

    /**
     * Generate booking confirmation message
     */
    private function generateBookingConfirmation(Appointment $appointment): string
    {
        $date = Carbon::parse($appointment->appointment_date)->format('l, M j, Y');
        $time = Carbon::parse($appointment->time_slot)->format('g:i A');

        return "🎉 **Appointment Confirmed!**\n\n" .
            "📅 **Date:** {$date}\n" .
            "⏰ **Time:** {$time}\n" .
            "📝 **Confirmation ID:** #{$appointment->id}\n\n" .
            "Thank you for booking with us! We look forward to seeing you. " .
            "If you need to reschedule or cancel, please contact us directly.";
    }

    /**
     * Get conversation context for maintaining chat history
     */
    private function getConversationContext(string $conversationId): array
    {
        $messages = $this->messageRepository->getConversationHistory($conversationId, 10);

        $context = [];
        foreach ($messages as $msg) {
            $context[] = [
                'role'    => 'user',
                'content' => $msg->message,
            ];

            if ($msg->reply) {
                $context[] = [
                    'role'    => 'assistant',
                    'content' => $msg->reply,
                ];
            }
        }

        return $context;
    }

    /**
     * Find relevant knowledge using vector similarity search
     */
    private function findRelevantKnowledge(string $userMessage): array
    {
        // Skip knowledge search for simple greetings and short messages
        $simpleGreetings = ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening', 'thanks', 'thank you', 'bye', 'goodbye'];
        $lowerMessage    = strtolower(trim($userMessage));

        if (strlen($lowerMessage) <= 10 || in_array($lowerMessage, $simpleGreetings)) {
            Log::info('Skipping knowledge search for simple greeting', ['message' => $userMessage]);
            return [];
        }

        try {
            // Generate embedding for user message
            $embedding = $this->useOllama && $this->ollamaService
            ? $this->generateOllamaEmbedding($userMessage)
            : $this->generateGeminiEmbedding($userMessage);

            // Find similar chunks
            $similarChunks = $this->knowledgeRepository->findSimilarChunksWithThreshold(
                $embedding,
                0.7, // similarity threshold
                5// max chunks
            );

            return $similarChunks->map(function ($chunk) {
                return [
                    'content'  => $chunk->content,
                    'source'   => $chunk->knowledgeFile->original_name ?? 'Unknown',
                    'distance' => $chunk->distance ?? null,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::warning('Failed to find relevant knowledge', [
                'error'   => $e->getMessage(),
                'message' => $userMessage,
            ]);

            return [];
        }
    }

    /**
     * Generate text embedding using Gemini
     */
    private function generateGeminiEmbedding(string $text): array
    {
        try {
            $response = $this->geminiClient->embeddingModel(ModelName::EMBEDDING_001)
                ->embedContent(new TextPart($text));

            return $response->embedding->values;
        } catch (Exception $e) {
            Log::error('Failed to generate Gemini embedding', [
                'error' => $e->getMessage(),
                'text'  => substr($text, 0, 100),
            ]);
            throw $e;
        }
    }

    /**
     * Generate text embedding using Ollama
     */
    private function generateOllamaEmbedding(string $text): array
    {
        try {
            return $this->ollamaService->generateEmbedding($text);
        } catch (Exception $e) {
            Log::error('Failed to generate Ollama embedding, falling back to Gemini', [
                'error' => $e->getMessage(),
                'text'  => substr($text, 0, 100),
            ]);

            // Fallback to Gemini if Ollama fails
            return $this->generateGeminiEmbedding($text);
        }
    }

    /**
     * Build conversation prompt with context, knowledge, and business info
     */
    private function buildPrompt(string $userMessage, array $conversationHistory, array $relevantKnowledge, array $appointmentIntent): string
    {
        $businessInfo = $this->getBusinessContext();

        $systemPrompt = "You are a friendly AI assistant for our car care business. ";
        $systemPrompt .= "You help customers with service information, pricing, and appointment booking. ";
        $systemPrompt .= "Be conversational, helpful, and professional. ";
        $systemPrompt .= "IMPORTANT: Keep responses short and concise for Instagram messaging (under 600 characters when possible). ";
        $systemPrompt .= "Break complex information into simple, digestible parts. Use emojis sparingly and focus on the most relevant information. ";

        $systemPrompt .= "\n\nBUSINESS INFORMATION:\n";
        $systemPrompt .= $businessInfo;

        if (!empty($relevantKnowledge)) {
            $systemPrompt .= "\n\nRELEVANT KNOWLEDGE:\n";
            foreach ($relevantKnowledge as $knowledge) {
                $systemPrompt .= "- " . $knowledge['content'] . "\n";
            }
        }

        if ($appointmentIntent['detected']) {
            $systemPrompt .= "\n\nAPPOINTMENT CONTEXT:\n";
            $systemPrompt .= "The user is interested in booking an appointment. ";
            $systemPrompt .= "Help them choose a service and schedule a time slot. ";
            $systemPrompt .= "Be proactive in offering available times and confirming details.";
        }

        $fullPrompt = $systemPrompt . "\n\nCONVERSATION:\n";

        // Add conversation history
        foreach ($conversationHistory as $msg) {
            $role = $msg['role'] === 'user' ? 'Customer' : 'Assistant';
            $fullPrompt .= "{$role}: {$msg['content']}\n";
        }

        $fullPrompt .= "Customer: {$userMessage}\nAssistant: ";

        return $fullPrompt;
    }

    /**
     * Get business context for AI prompts
     */
    private function getBusinessContext(): string
    {
        $services      = BusinessConfig::getServices();
        $businessHours = BusinessConfig::get('business_hours', []);

        $context = "SERVICES OFFERED:\n";
        foreach ($services as $key => $service) {
            $context .= "- {$service['name']}: \${$service['price']} ({$service['duration']} minutes) - {$service['description']}\n";
        }

        $context .= "\nBUSINESS HOURS:\n";
        foreach ($businessHours as $day => $hours) {
            if ($hours) {
                $start = Carbon::createFromFormat('H:i', $hours[0])->format('g:i A');
                $end   = Carbon::createFromFormat('H:i', $hours[1])->format('g:i A');
                $context .= "- " . ucfirst($day) . ": {$start} - {$end}\n";
            } else {
                $context .= "- " . ucfirst($day) . ": Closed\n";
            }
        }

        return $context;
    }

    /**
     * Send message to Instagram user
     */
    private function sendMessage(string $recipientId, string $message, ?string $pageId = null): bool
    {
        try {
            // Use InstagramService which handles token management and refresh automatically
            return $this->instagramService->sendMessage($recipientId, $message, $pageId);

        } catch (Exception $e) {
            Log::error('Exception sending message through InstagramService', [
                'recipient_id' => $recipientId,
                'page_id'      => $pageId,
                'error'        => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Send typing indicator to improve user experience
     */
    private function sendTypingIndicator(string $recipientId, ?string $pageId = null): void
    {
        try {
            // Get active token from InstagramService
            $token = $this->instagramService->getActiveToken($pageId);
            if (!$token) {
                Log::warning('No active Instagram token for typing indicator');
                return;
            }

            $response = Http::post("https://graph.instagram.com/v23.0/{$token->instagram_business_id}/messages", [
                'recipient'     => ['id' => $recipientId],
                'sender_action' => 'typing_on',
                'access_token'  => $token->access_token,
            ]);

            if (!$response->successful()) {
                Log::warning('Failed to send typing indicator', [
                    'error' => $response->json(),
                ]);
            }
        } catch (Exception $e) {
            Log::warning('Failed to send typing indicator', [
                'recipient_id' => $recipientId,
                'page_id'      => $pageId,
                'error'        => $e->getMessage(),
            ]);
        }
    }

    /**
     * Rate limiting to prevent spam
     */
    private function checkRateLimit(string $senderId): void
    {
        $key          = "rate_limit:chatbot:{$senderId}";
        $currentCount = Cache::get($key, 0);

        if ($currentCount >= 1) { // 1 message per second
            throw new Exception('Rate limit exceeded');
        }

        Cache::put($key, $currentCount + 1, 1); // 1 second
    }

    /**
     * Get conversation statistics
     */
    public function getConversationStats(string $conversationId): array
    {
        $messages = $this->messageRepository->getConversationHistory($conversationId);

        return [
            'total_messages' => $messages->count(),
            'last_activity'  => $messages->last()?->created_at,
            'response_rate'  => $messages->where('reply', '!=', null)->count() / max($messages->count(), 1),
        ];
    }

    /**
     * Ensure response length is appropriate for Instagram messaging
     */
    private function ensureResponseLength(string $response): string
    {
        // Instagram has a 1000 character limit, but we want to keep responses shorter when possible
        $optimalLength = 800; // Leave room for chunking indicators if needed

        if (strlen($response) <= $optimalLength) {
            return $response;
        }

        Log::info('Response too long, truncating', [
            'original_length' => strlen($response),
            'optimal_length'  => $optimalLength,
        ]);

        // Try to find a natural breaking point near the optimal length
        $truncated = substr($response, 0, $optimalLength);

        // Look for the last complete sentence
        $lastPeriod      = strrpos($truncated, '.');
        $lastExclamation = strrpos($truncated, '!');
        $lastQuestion    = strrpos($truncated, '?');

        $lastSentenceEnd = max($lastPeriod, $lastExclamation, $lastQuestion);

        if ($lastSentenceEnd && $lastSentenceEnd > $optimalLength * 0.7) {
            // Use the last complete sentence if it's not too short
            $truncated = substr($response, 0, $lastSentenceEnd + 1);
        } else {
            // Look for last complete word
            $lastSpace = strrpos($truncated, ' ');
            if ($lastSpace) {
                $truncated = substr($response, 0, $lastSpace);
            }
            $truncated .= '...';
        }

        return $truncated;
    }
}
