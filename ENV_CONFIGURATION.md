# Environment Configuration for Kortana AI

## Required .env Variables

Based on your Meta Developer Console, add these variables to your `.env` file:

### Meta/Facebook App Configuration

```env
# From your Meta Developer Console
META_APP_ID=****************
META_APP_SECRET=your_app_secret_here
META_WEBHOOK_VERIFY_TOKEN=kortana_webhook_verify_2024

# Instagram App Details (from your console)
INSTAGRAM_APP_NAME=kortana01-IG
INSTAGRAM_APP_ID=****************
INSTAGRAM_APP_SECRET=c154b2c349e4cadc9b48c
```

### Access Tokens (You Need to Generate These)

```env
# Long-lived Page Access Token (generate via Graph API Explorer)
META_PAGE_ACCESS_TOKEN=your_long_lived_page_access_token

# Instagram Business Account ID (get from Graph API)
INSTAGRAM_BUSINESS_ACCOUNT_ID=your_instagram_business_account_id

# Facebook Page ID connected to Instagram
INSTAGRAM_PAGE_ID=your_facebook_page_id
```

### Webhook Configuration

```env
# Webhook URLs for your Laravel app
INSTAGRAM_WEBHOOK_URL=http://kortana-ai.test/api/webhooks/instagram
FACEBOOK_WEBHOOK_URL=http://kortana-ai.test/api/webhooks/facebook
```

### Laravel App Configuration

```env
APP_URL=http://kortana-ai.test
APP_ENV=local
APP_DEBUG=true
```

## Next Steps to Complete Setup:

### 1. Generate Access Token

1. Go to [Graph API Explorer](https://developers.facebook.com/tools/explorer/)
2. Select your app: `kortana01-IG` (ID: ****************)
3. Click "Generate Access Token"
4. Grant these permissions:
    - `pages_show_list`
    - `pages_read_engagement`
    - `pages_manage_metadata`
    - `instagram_basic`
    - `instagram_manage_messages`
    - `instagram_manage_comments`

### 2. Exchange for Long-Lived Token

```bash
curl -X GET "https://graph.facebook.com/v23.0/oauth/access_token" \
  -d "grant_type=fb_exchange_token" \
  -d "client_id=****************" \
  -d "client_secret=YOUR_APP_SECRET" \
  -d "fb_exchange_token=YOUR_SHORT_LIVED_TOKEN"
```

### 3. Get Instagram Business Account ID

```bash
# First get your Facebook Pages
curl -X GET "https://graph.facebook.com/v23.0/me/accounts" \
  -d "access_token=YOUR_LONG_LIVED_TOKEN"

# Then get Instagram Business Account connected to your page
curl -X GET "https://graph.facebook.com/v23.0/YOUR_PAGE_ID" \
  -d "fields=instagram_business_account" \
  -d "access_token=YOUR_LONG_LIVED_TOKEN"
```

### 4. Configure Webhooks in Meta Console

In your Meta Developer Console:

1. Go to **Instagram > Configuration**
2. Set **Callback URL**: `http://kortana-ai.test/api/webhooks/instagram`
3. Set **Verify Token**: `kortana_webhook_verify_2024`
4. Click "Verify and Save"

### 5. Test Webhook Verification

```bash
curl -X GET "http://kortana-ai.test/api/webhooks/instagram?hub.mode=subscribe&hub.challenge=test123&hub.verify_token=kortana_webhook_verify_2024"
```

Should return: `test123`

## Complete .env Example

```env
# Laravel Configuration
APP_NAME="Kortana AI"
APP_ENV=local
APP_KEY=base64:your_app_key_here
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://kortana-ai.test

# Database
DB_CONNECTION=sqlite

# Meta/Facebook Configuration
META_APP_ID=****************
META_APP_SECRET=your_actual_app_secret
META_WEBHOOK_VERIFY_TOKEN=kortana_webhook_verify_2024
META_PAGE_ACCESS_TOKEN=your_long_lived_page_access_token

# Instagram Configuration
INSTAGRAM_APP_NAME=kortana01-IG
INSTAGRAM_APP_ID=****************
INSTAGRAM_APP_SECRET=c154b2c349e4cadc9b48c
INSTAGRAM_BUSINESS_ACCOUNT_ID=your_instagram_business_id
INSTAGRAM_PAGE_ID=your_facebook_page_id

# Webhook URLs
INSTAGRAM_WEBHOOK_URL=http://kortana-ai.test/api/webhooks/instagram
FACEBOOK_WEBHOOK_URL=http://kortana-ai.test/api/webhooks/facebook

# Cache & Session
CACHE_STORE=database
SESSION_DRIVER=database
QUEUE_CONNECTION=database

# Mail
MAIL_MAILER=log

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=debug
```

## Important Notes:

1. **App Secret**: Get this from your Meta Developer Console under App Settings > Basic
2. **Verify Token**: Use a custom string like `kortana_webhook_verify_2024`
3. **Access Token**: Must be long-lived (60 days) for production use
4. **Business Account ID**: Different from your personal Instagram ID
5. **Page ID**: The Facebook page connected to your Instagram Business account

After updating your `.env` file, run:

```bash
php artisan config:clear
php artisan cache:clear
```
