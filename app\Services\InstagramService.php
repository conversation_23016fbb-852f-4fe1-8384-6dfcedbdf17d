<?php

declare (strict_types = 1);

namespace App\Services;

use App\Models\Message;
use App\Models\MetaToken;
use App\Repositories\MessageRepository;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class InstagramService
{
    public function __construct(
        private readonly MessageRepository $messageRepository
    ) {}

    /**
     * Send message to Instagram user
     */
    public function sendMessage(string $recipientId, string $message, ?string $pageId = null): bool
    {
        try {
            Log::info('Attempting to send Instagram message', [
                'recipient_id'   => $recipientId,
                'page_id'        => $pageId,
                'message_length' => strlen($message),
            ]);

            // Get active token with automatic refresh
            $token = $this->getActiveToken($pageId);
            if (!$token) {
                Log::error('No valid Instagram access token available', [
                    'page_id' => $pageId,
                ]);
                return false;
            }

            // Log token details for debugging
            Log::info('Using Instagram token', [
                'token_id'     => $token->id,
                'app_scope_id' => $token->app_scope_id,
                'user_id'      => $token->user_id,
                'username'     => $token->username,
            ]);

            // Use the Instagram app-scoped ID for sending messages
            $instagramAccountId = $token->app_scope_id;

            // Instagram has a 1000 character limit
            $messageChunks = $this->chunkMessage($message, 950);

            foreach ($messageChunks as $chunk) {
                $response = Http::post("https://graph.instagram.com/v23.0/{$instagramAccountId}/messages", [
                    'recipient'    => [
                        'id' => $recipientId,
                    ],
                    'message'      => [
                        'text' => $chunk,
                    ],
                    'access_token' => $token->access_token,
                ]);

                if ($response->successful()) {
                    Log::info('Successfully sent Instagram message', [
                        'recipient_id' => $recipientId,
                        'account_id'   => $instagramAccountId,
                        'page_id'      => $pageId,
                    ]);
                    return true;
                }

                $error = $response->json();
                Log::error('Failed to send Instagram message', [
                    'error'         => $error,
                    'recipient_id'  => $recipientId,
                    'account_id'    => $instagramAccountId,
                    'page_id'       => $pageId,
                    'token_details' => [
                        'app_scope_id' => $token->app_scope_id,
                        'user_id'      => $token->user_id,
                        'username'     => $token->username,
                    ],
                ]);

                // If token is invalid, mark it as inactive
                if (isset($error['error']['code']) && $error['error']['code'] === 190) {
                    Log::warning('Marking Instagram token as inactive due to OAuth error', [
                        'token_id' => $token->id,
                    ]);
                    $token->update([
                        'is_active' => false,
                        'status'    => 'invalid',
                    ]);
                }

                return false;
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Exception in Instagram sendMessage', [
                'error'        => $e->getMessage(),
                'recipient_id' => $recipientId,
                'page_id'      => $pageId,
            ]);
            return false;
        }
    }

    /**
     * Chunk a long message into smaller pieces that fit Instagram's character limit
     */
    private function chunkMessage(string $message, int $maxLength = 950): array
    {
        // If message is short enough, return as single chunk
        if (strlen($message) <= $maxLength) {
            return [$message];
        }

        $chunks       = [];
        $currentChunk = '';

        // Split by paragraphs first (double newlines)
        $paragraphs = explode("\n\n", $message);

        foreach ($paragraphs as $paragraph) {
            // If adding this paragraph would exceed limit
            if (strlen($currentChunk . "\n\n" . $paragraph) > $maxLength && !empty($currentChunk)) {
                // Save current chunk and start new one
                $chunks[]     = trim($currentChunk);
                $currentChunk = $paragraph;
            } else {
                // Add paragraph to current chunk
                if (!empty($currentChunk)) {
                    $currentChunk .= "\n\n" . $paragraph;
                } else {
                    $currentChunk = $paragraph;
                }
            }

            // If single paragraph is too long, split by sentences
            if (strlen($currentChunk) > $maxLength) {
                $sentences    = preg_split('/(?<=[.!?])\s+/', $currentChunk);
                $currentChunk = '';

                foreach ($sentences as $sentence) {
                    if (strlen($currentChunk . ' ' . $sentence) > $maxLength && !empty($currentChunk)) {
                        $chunks[]     = trim($currentChunk);
                        $currentChunk = $sentence;
                    } else {
                        if (!empty($currentChunk)) {
                            $currentChunk .= ' ' . $sentence;
                        } else {
                            $currentChunk = $sentence;
                        }
                    }

                    // If single sentence is still too long, split by words
                    if (strlen($currentChunk) > $maxLength) {
                        $words        = explode(' ', $currentChunk);
                        $currentChunk = '';

                        foreach ($words as $word) {
                            if (strlen($currentChunk . ' ' . $word) > $maxLength && !empty($currentChunk)) {
                                $chunks[]     = trim($currentChunk);
                                $currentChunk = $word;
                            } else {
                                if (!empty($currentChunk)) {
                                    $currentChunk .= ' ' . $word;
                                } else {
                                    $currentChunk = $word;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Add the last chunk if not empty
        if (!empty($currentChunk)) {
            $chunks[] = trim($currentChunk);
        }

        // Add continuation indicators for multi-part messages
        if (count($chunks) > 1) {
            for ($i = 0; $i < count($chunks); $i++) {
                if ($i === 0) {
                    $chunks[$i] = $chunks[$i] . "\n\n(1/" . count($chunks) . ")";
                } else {
                    $chunks[$i] = "(" . ($i + 1) . "/" . count($chunks) . ")\n\n" . $chunks[$i];
                }
            }
        }

        return $chunks;
    }

    /**
     * Process incoming Instagram message
     */
    public function processIncomingMessage(array $messageData): Message
    {
        // Store the message
        $message = $this->messageRepository->create([
            'sender_id'       => $messageData['sender_id'],
            'conversation_id' => $messageData['conversation_id'],
            'content'         => $messageData['content'],
            'platform'        => 'instagram',
            'sent_at'         => now(),
            'status'          => 'received',
            'meta_data'       => $messageData['meta_data'] ?? [],
        ]);

        Log::info('Instagram message processed', [
            'message_id'      => $message->id,
            'sender_id'       => $message->sender_id,
            'conversation_id' => $message->conversation_id,
        ]);

        return $message;
    }

    /**
     * Verify webhook token
     */
    public function verifyWebhookToken(string $token): bool
    {
        $verifyToken = config('services.meta.webhook_verify_token');
        return $token === $verifyToken;
    }

    /**
     * Get Instagram user profile
     */
    public function getUserProfile(string $userId): ?array
    {
        try {
            $accessToken = config('services.meta.access_token');

            if (!$accessToken) {
                return null;
            }

            $response = Http::get("https://graph.facebook.com/v23.0/{$userId}", [
                'fields'       => 'name,profile_pic',
                'access_token' => $accessToken,
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::warning('Failed to get Instagram user profile', [
                'user_id' => $userId,
                'status'  => $response->status(),
            ]);

            return null;

        } catch (Exception $e) {
            Log::error('Instagram user profile fetch failed', [
                'user_id' => $userId,
                'error'   => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get active Instagram token
     */
    public function getActiveToken(?string $pageId = null): ?MetaToken
    {
        try {
            Log::info('Getting active Instagram token', [
                'page_id' => $pageId,
            ]);

            $query = MetaToken::where('platform', 'instagram')
                ->where('is_active', true);

            // If page ID is provided, try to match with app_scope_id or user_id
            if ($pageId) {
                $query->where(function ($q) use ($pageId) {
                    $q->where('app_scope_id', $pageId)
                        ->orWhere('user_id', $pageId)
                        ->orWhere('page_id', $pageId);
                });
            }

            $token = $query->first();

            if (!$token) {
                Log::warning('No matching token found for page', [
                    'page_id' => $pageId,
                ]);

                // If no specific token found, get any active Instagram token
                $token = MetaToken::where('platform', 'instagram')
                    ->where('is_active', true)
                    ->first();
            }

            if (!$token) {
                Log::error('No active Instagram token found');
                return null;
            }

            Log::info('Found active Instagram token', [
                'token_id'     => $token->id,
                'app_scope_id' => $token->app_scope_id,
                'user_id'      => $token->user_id,
                'username'     => $token->username,
                'page_id'      => $token->page_id,
            ]);

            return $token;
        } catch (\Exception $e) {
            Log::error('Error getting active Instagram token: ' . $e->getMessage(), [
                'page_id' => $pageId,
                'error'   => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Refresh an expired Instagram access token
     */
    private function refreshToken(MetaToken $token): ?MetaToken
    {
        try {
            $response = Http::get('https://graph.instagram.com/refresh_access_token', [
                'grant_type'   => 'ig_refresh_token',
                'access_token' => $token->access_token,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                $token->update([
                    'access_token' => $data['access_token'],
                    'token_type'   => $data['token_type'],
                    'expires_at'   => now()->addSeconds($data['expires_in']),
                    'is_active'    => true,
                ]);

                Log::info('Instagram token refreshed successfully', [
                    'new_expires_at' => $token->fresh()->expires_at,
                    'valid_for_days' => round($data['expires_in'] / 86400),
                ]);

                return $token->fresh();
            } else {
                $error = $response->json();
                Log::error('Instagram token refresh failed', [
                    'error'         => $error,
                    'response_code' => $response->status(),
                ]);

                // Mark token as inactive if refresh fails
                $token->update(['is_active' => false]);
                return null;
            }
        } catch (Exception $e) {
            Log::error('Instagram token refresh exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }

    /**
     * Find the Instagram account associated with a sender/recipient pair
     *
     * @param string $senderId The ID of the sender
     * @param string $recipientId The ID of the recipient
     * @return MetaToken|null The associated Instagram account token or null if not found
     */
    public function findAccountForMessage(string $senderId, string $recipientId): ?MetaToken
    {
        try {
            // Try to find a token where either the app_scope_id or user_id matches
            // either the sender or recipient ID
            $token = MetaToken::where('platform', 'instagram')
                ->where('is_active', true)
                ->where(function ($query) use ($senderId, $recipientId) {
                    $query->where('app_scope_id', $senderId)
                        ->orWhere('app_scope_id', $recipientId)
                        ->orWhere('user_id', $senderId)
                        ->orWhere('user_id', $recipientId);
                })
                ->first();

            if ($token) {
                Log::info('Found Instagram account for message', [
                    'sender_id'    => $senderId,
                    'recipient_id' => $recipientId,
                    'token_id'     => $token->id,
                    'username'     => $token->username,
                    'account_type' => $token->account_type,
                    'app_scope_id' => $token->app_scope_id,
                    'user_id'      => $token->user_id,
                ]);

                return $token;
            }

            // If no direct match, it could be a message from/to a user messaging the business
            // Check if we have any active Instagram tokens at all
            $token = MetaToken::where('platform', 'instagram')
                ->where('is_active', true)
                ->first();

            if ($token) {
                Log::info('Using default Instagram account for message', [
                    'sender_id'    => $senderId,
                    'recipient_id' => $recipientId,
                    'token_id'     => $token->id,
                    'username'     => $token->username,
                ]);

                return $token;
            }

            Log::warning('No Instagram account found for message', [
                'sender_id'    => $senderId,
                'recipient_id' => $recipientId,
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('Error finding Instagram account for message', [
                'error'        => $e->getMessage(),
                'sender_id'    => $senderId,
                'recipient_id' => $recipientId,
            ]);

            return null;
        }
    }
}