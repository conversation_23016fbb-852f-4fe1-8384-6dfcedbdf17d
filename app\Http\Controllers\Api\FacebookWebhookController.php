<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\FacebookWebhookRequest;
use App\Services\ChatbotService;
use App\Services\FacebookService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class FacebookWebhookController extends Controller
{
    public function __construct(
        private readonly FacebookService $facebookService,
        private readonly ChatbotService $chatbotService
    ) {}

    public function verify(Request $request): JsonResponse|\Illuminate\Http\Response
    {
        $mode = $request->query('hub_mode');
        $token = $request->query('hub_verify_token');
        $challenge = $request->query('hub_challenge');

        if ($mode === 'subscribe' && $this->facebookService->verifyWebhookToken($token)) {
            Log::info('Facebook webhook verified successfully');
            return response($challenge, 200);
        }

        return response()->json(['error' => 'Forbidden'], 403);
    }

    public function webhook(FacebookWebhookRequest $request): JsonResponse
    {
        try {
            if ($request->isMessage()) {
                $messageData = $request->getMessageData();
                $message = $this->chatbotService->processMessage($messageData);
                return response()->json(['status' => 'success', 'message_id' => $message->id]);
            }

            return response()->json(['status' => 'ignored']);
        } catch (Exception $e) {
            return response()->json(['status' => 'error'], 200);
        }
    }
}