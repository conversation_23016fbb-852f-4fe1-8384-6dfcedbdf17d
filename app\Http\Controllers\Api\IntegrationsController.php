<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MetaToken;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;

final class IntegrationsController extends Controller
{
    use ApiResponseTrait;

    /**
     * Get all integrations status
     */
    public function index(): JsonResponse
    {
        $integrations = [
            'social_media'  => [
                'linkedin'  => [
                    'name'        => 'LinkedIn',
                    'status'      => 'connected',
                    'description' => 'Auto-respond to DMs and comments',
                    'enabled'     => true,
                ],
                'twitter'   => [
                    'name'        => 'Twitter',
                    'status'      => 'disconnected',
                    'description' => 'Manage tweets and direct messages',
                    'enabled'     => false,
                ],
                'facebook'  => [
                    'name'        => 'Facebook',
                    'status'      => 'connected',
                    'description' => 'Handle page messages and comments',
                    'enabled'     => false,
                ],
                'instagram' => [
                    'name'        => 'Instagram',
                    'status'      => $this->getInstagramStatus(),
                    'description' => 'Respond to comments and DMs',
                    'enabled'     => false,
                    'auth_url'    => $this->getInstagramAuthUrl(),
                ],
            ],
            'communication' => [
                'whatsapp_business' => [
                    'name'        => 'WhatsApp Business',
                    'status'      => 'connected',
                    'description' => 'Auto-respond to DMs and comments',
                    'enabled'     => true,
                ],
                'gmail'             => [
                    'name'        => 'Gmail',
                    'status'      => 'disconnected',
                    'description' => 'Manage tweets and direct messages',
                    'enabled'     => false,
                ],
            ],
            'reviews'       => [
                'google_reviews'     => [
                    'name'        => 'Google Reviews',
                    'status'      => 'connected',
                    'description' => 'Auto-respond to customer reviews',
                    'enabled'     => true,
                ],
                'trustpilot_reviews' => [
                    'name'        => 'Trustpilot Reviews',
                    'status'      => 'disconnected',
                    'description' => 'Auto-respond to customer reviews',
                    'enabled'     => false,
                ],
            ],
            'others'        => [
                'zapier' => [
                    'name'        => 'Zapier',
                    'status'      => 'connected',
                    'description' => 'Auto-respond to customer reviews',
                    'enabled'     => true,
                ],
            ],
        ];

        $activeCount = $this->countActiveIntegrations($integrations);
        $totalCount  = $this->countTotalIntegrations($integrations);

        return $this->successResponse([
            'integrations' => $integrations,
            'active_count' => $activeCount,
            'total_count'  => $totalCount,
        ]);
    }

    /**
     * Configure a specific integration
     */
    public function configure(string $category, string $integration): JsonResponse
    {
        // Validate category and integration
        $validCategories = ['social_media', 'communication', 'reviews', 'others'];
        if (!in_array($category, $validCategories)) {
            return $this->errorResponse('Invalid integration category', 400);
        }

        // Get configuration data based on integration type
        $configData = [];

        switch ($category . '.' . $integration) {
            case 'social_media.instagram':
                $configData = [
                    'auth_url'          => $this->getInstagramAuthUrl(),
                    'status'            => $this->getInstagramStatus(),
                    'connected_account' => $this->getInstagramConnectedAccount(),
                ];
                break;

            case 'social_media.facebook':
                $configData = [
                    'status' => 'connected',
                    'pages'  => [
                        ['id' => '*********', 'name' => 'Business Page 1'],
                        ['id' => '*********', 'name' => 'Business Page 2'],
                    ],
                ];
                break;

            case 'others.zapier':
                $configData = [
                    'status'      => 'connected',
                    'api_key'     => '••••••••' . substr(md5(time()), 0, 8),
                    'webhook_url' => config('app.url') . '/api/webhooks/zapier',
                    'triggers'    => [
                        'new_message'        => true,
                        'new_review'         => true,
                        'appointment_booked' => false,
                    ],
                ];
                break;

            default:
                return $this->errorResponse('Integration not found or not configurable', 404);
        }

        return $this->successResponse([
            'integration' => [
                'category' => $category,
                'key'      => $integration,
                'config'   => $configData,
            ],
        ]);
    }

    /**
     * Get Instagram authentication URL
     */
    public function getInstagramAuthUrl(): string
    {
        // Generate a random state parameter for security
        $state = Str::random(40);
        session(['instagram_oauth_state' => $state]);

        $clientId    = Config::get('services.instagram.app_id');
        $redirectUri = Config::get('services.instagram.redirect_uri');

        // Define scopes for Instagram Business API
        $scope = 'instagram_business_basic,instagram_business_manage_messages,instagram_business_manage_comments,instagram_business_content_publish,instagram_business_manage_insights';

        // Instagram OAuth URL
        return "https://www.instagram.com/oauth/authorize?" . http_build_query([
            'client_id'     => $clientId,
            'redirect_uri'  => $redirectUri,
            'scope'         => $scope,
            'response_type' => 'code',
            'state'         => $state,
            'force_reauth'  => 'true',
        ]);
    }

    /**
     * Count active integrations
     */
    private function countActiveIntegrations(array $integrations): int
    {
        $count = 0;
        foreach ($integrations as $category) {
            foreach ($category as $integration) {
                if ($integration['status'] === 'connected') {
                    $count++;
                }
            }
        }
        return $count;
    }

    /**
     * Count total integrations
     */
    private function countTotalIntegrations(array $integrations): int
    {
        $count = 0;
        foreach ($integrations as $category) {
            $count += count($category);
        }
        return $count;
    }

    /**
     * Get Instagram connection status
     */
    private function getInstagramStatus(): string
    {
        $token = MetaToken::where('platform', 'instagram')
            ->where('is_active', true)
            ->first();

        return $token ? 'connected' : 'disconnected';
    }

    /**
     * Get Instagram connected account details
     */
    private function getInstagramConnectedAccount(): ?array
    {
        $token = MetaToken::where('platform', 'instagram')
            ->where('is_active', true)
            ->first();

        if (!$token) {
            return null;
        }

        return [
            'username'            => $token->username,
            'name'                => $token->name,
            'profile_picture_url' => $token->profile_picture_url,
            'followers_count'     => $token->followers_count,
            'follows_count'       => $token->follows_count,
            'media_count'         => $token->media_count,
            'connected_at'        => $token->created_at->format('Y-m-d H:i:s'),
            'expires_at'          => $token->expires_at ? $token->expires_at->format('Y-m-d H:i:s') : null,
        ];
    }
}