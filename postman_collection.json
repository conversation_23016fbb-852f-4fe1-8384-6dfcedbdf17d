{"info": {"name": "Kortana AI - Social Media Integration API", "description": "Complete API collection for testing social media integrations, webhooks, and business configuration endpoints", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://kortana-ai.test", "type": "string"}, {"key": "auth_token", "value": "your_bearer_token_here", "type": "string"}, {"key": "user_id", "value": "1", "type": "string"}], "item": [{"name": "Social Media Endpoints", "item": [{"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"platform\": \"instagram\",\n  \"recipient_id\": \"**********1234567\",\n  \"message\": \"Hello! How can I help you today?\",\n  \"message_type\": \"text\",\n  \"metadata\": {\n    \"context\": \"customer_support\",\n    \"priority\": \"normal\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/social/message", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "message"]}}}, {"name": "Send Instagram Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipient_id\": \"**********1234567\",\n  \"message\": \"Welcome to our Instagram support!\",\n  \"message_type\": \"text\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/social/instagram/message", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "message"]}}}, {"name": "Send Facebook Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipient_id\": \"**********1234567\",\n  \"message\": \"Thanks for contacting us on Facebook!\",\n  \"message_type\": \"text\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/social/facebook/message", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "facebook", "message"]}}}, {"name": "Send Twitter Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipient_id\": \"twitter_user_id\",\n  \"message\": \"Hello from Twitter support!\",\n  \"message_type\": \"direct_message\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/social/twitter/message", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "twitter", "message"]}}}, {"name": "Get Platform Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/social/stats?platform=instagram", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "stats"], "query": [{"key": "platform", "value": "instagram"}]}}}, {"name": "Get All Platform Messages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/social/messages?platform=instagram&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "messages"], "query": [{"key": "platform", "value": "instagram"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Instagram Messages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/messages?limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "messages"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Get Facebook Messages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/social/facebook/messages?limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "facebook", "messages"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Get Twitter Messages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/social/twitter/messages?limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "twitter", "messages"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Get Conversation History", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/social/conversations?platform=instagram&user_id=**********1234567", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "conversations"], "query": [{"key": "platform", "value": "instagram"}, {"key": "user_id", "value": "**********1234567"}]}}}, {"name": "Get Active Conversations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/social/conversations/active?platform=instagram", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "conversations", "active"], "query": [{"key": "platform", "value": "instagram"}]}}}, {"name": "Get Pending Messages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/social/messages/pending?platform=facebook", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "messages", "pending"], "query": [{"key": "platform", "value": "facebook"}]}}}, {"name": "Get Hourly Distribution", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/social/analytics/hourly?platform=twitter&date=2024-01-15", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "analytics", "hourly"], "query": [{"key": "platform", "value": "twitter"}, {"key": "date", "value": "2024-01-15"}]}}}]}, {"name": "Webhook Endpoints", "item": [{"name": "Instagram Webhook (GET - Verification)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/webhooks/instagram?hub.mode=subscribe&hub.challenge=test_challenge&hub.verify_token=your_verify_token", "host": ["{{base_url}}"], "path": ["api", "webhooks", "instagram"], "query": [{"key": "hub.mode", "value": "subscribe"}, {"key": "hub.challenge", "value": "test_challenge"}, {"key": "hub.verify_token", "value": "your_verify_token"}]}}}, {"name": "Instagram Webhook (POST - Message)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"object\": \"instagram\",\n  \"entry\": [\n    {\n      \"id\": \"page_id\",\n      \"time\": **********,\n      \"messaging\": [\n        {\n          \"sender\": {\n            \"id\": \"**********1234567\"\n          },\n          \"recipient\": {\n            \"id\": \"your_page_id\"\n          },\n          \"timestamp\": **********,\n          \"message\": {\n            \"mid\": \"message_id\",\n            \"text\": \"Hello, I need help with my order\"\n          }\n        }\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/webhooks/instagram", "host": ["{{base_url}}"], "path": ["api", "webhooks", "instagram"]}}}, {"name": "Facebook Webhook (GET - Verification)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/webhooks/facebook?hub.mode=subscribe&hub.challenge=test_challenge&hub.verify_token=your_verify_token", "host": ["{{base_url}}"], "path": ["api", "webhooks", "facebook"], "query": [{"key": "hub.mode", "value": "subscribe"}, {"key": "hub.challenge", "value": "test_challenge"}, {"key": "hub.verify_token", "value": "your_verify_token"}]}}}, {"name": "Facebook Webhook (POST - Message)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"object\": \"page\",\n  \"entry\": [\n    {\n      \"id\": \"page_id\",\n      \"time\": **********,\n      \"messaging\": [\n        {\n          \"sender\": {\n            \"id\": \"**********1234567\"\n          },\n          \"recipient\": {\n            \"id\": \"your_page_id\"\n          },\n          \"timestamp\": **********,\n          \"message\": {\n            \"mid\": \"message_id\",\n            \"text\": \"Hi there! I'm interested in your services\"\n          }\n        }\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/webhooks/facebook", "host": ["{{base_url}}"], "path": ["api", "webhooks", "facebook"]}}}, {"name": "Facebook Webhook (POST - Postback)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"object\": \"page\",\n  \"entry\": [\n    {\n      \"id\": \"page_id\",\n      \"time\": **********,\n      \"messaging\": [\n        {\n          \"sender\": {\n            \"id\": \"**********1234567\"\n          },\n          \"recipient\": {\n            \"id\": \"your_page_id\"\n          },\n          \"timestamp\": **********,\n          \"postback\": {\n            \"payload\": \"GET_STARTED\"\n          }\n        }\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/webhooks/facebook", "host": ["{{base_url}}"], "path": ["api", "webhooks", "facebook"]}}}]}, {"name": "Business Configuration", "item": [{"name": "Get Business Config", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/business-config/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "business-config", "{{user_id}}"]}}}, {"name": "Create Business Config", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"business_name\": \"My Business\",\n  \"business_type\": \"E-commerce\",\n  \"business_description\": \"Online retail store specializing in electronics\",\n  \"operating_hours\": {\n    \"monday\": \"09:00-18:00\",\n    \"tuesday\": \"09:00-18:00\",\n    \"wednesday\": \"09:00-18:00\",\n    \"thursday\": \"09:00-18:00\",\n    \"friday\": \"09:00-18:00\",\n    \"saturday\": \"10:00-16:00\",\n    \"sunday\": \"closed\"\n  },\n  \"contact_info\": {\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+**********\",\n    \"website\": \"https://mybusiness.com\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/business-config", "host": ["{{base_url}}"], "path": ["api", "v1", "business-config"]}}}, {"name": "Update Business Config", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"business_name\": \"Updated Business Name\",\n  \"business_type\": \"Service Provider\",\n  \"business_description\": \"Professional consulting services\",\n  \"operating_hours\": {\n    \"monday\": \"08:00-17:00\",\n    \"tuesday\": \"08:00-17:00\",\n    \"wednesday\": \"08:00-17:00\",\n    \"thursday\": \"08:00-17:00\",\n    \"friday\": \"08:00-17:00\",\n    \"saturday\": \"closed\",\n    \"sunday\": \"closed\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/business-config/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "business-config", "{{user_id}}"]}}}, {"name": "Delete Business Config", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/business-config/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "business-config", "{{user_id}}"]}}}]}, {"name": "Agent Management", "item": [{"name": "Get Agent", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/agent/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "agent", "{{user_id}}"]}}}, {"name": "Create Agent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Customer Support Agent\",\n  \"personality\": \"friendly\",\n  \"instructions\": \"Always be helpful and professional when assisting customers\",\n  \"capabilities\": [\"customer_support\", \"order_tracking\", \"product_info\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/agent", "host": ["{{base_url}}"], "path": ["api", "v1", "agent"]}}}, {"name": "Update Agent", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Advanced Support Agent\",\n  \"personality\": \"professional\",\n  \"instructions\": \"Provide detailed technical support and escalate complex issues\",\n  \"capabilities\": [\"technical_support\", \"troubleshooting\", \"escalation\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/agent/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "agent", "{{user_id}}"]}}}]}, {"name": "Integrations", "item": [{"name": "Get Integrations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/integrations/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "integrations", "{{user_id}}"]}}}, {"name": "Create Integration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"platform\": \"instagram\",\n  \"access_token\": \"your_instagram_access_token\",\n  \"page_id\": \"your_instagram_page_id\",\n  \"webhook_url\": \"https://yourdomain.com/api/webhooks/instagram\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/integrations", "host": ["{{base_url}}"], "path": ["api", "v1", "integrations"]}}}, {"name": "Update Integration", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"access_token\": \"updated_access_token\",\n  \"is_active\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/integrations/1", "host": ["{{base_url}}"], "path": ["api", "v1", "integrations", "1"]}}}]}, {"name": "Scheduling", "item": [{"name": "Get Available Slots", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/scheduling/available-slots?date=2024-01-15&service_type=consultation", "host": ["{{base_url}}"], "path": ["api", "v1", "scheduling", "available-slots"], "query": [{"key": "date", "value": "2024-01-15"}, {"key": "service_type", "value": "consultation"}]}}}, {"name": "Book Appointment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_name\": \"<PERSON>\",\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_phone\": \"+**********\",\n  \"appointment_date\": \"2024-01-15\",\n  \"appointment_time\": \"14:00\",\n  \"service_type\": \"consultation\",\n  \"notes\": \"Initial consultation for business setup\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/scheduling/book", "host": ["{{base_url}}"], "path": ["api", "v1", "scheduling", "book"]}}}, {"name": "Get Appointments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/scheduling/appointments?date=2024-01-15", "host": ["{{base_url}}"], "path": ["api", "v1", "scheduling", "appointments"], "query": [{"key": "date", "value": "2024-01-15"}]}}}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Test Chatbot Response", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"Hello, I need help with my order\",\n  \"user_id\": \"**********1234567\",\n  \"platform\": \"instagram\",\n  \"context\": {\n    \"conversation_id\": \"conv_123\",\n    \"previous_messages\": []\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/chatbot/test", "host": ["{{base_url}}"], "path": ["api", "v1", "chatbot", "test"]}}}]}, {"name": "Knowledge Base", "item": [{"name": "Get Knowledge Files", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/knowledge/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge", "{{user_id}}"]}}}, {"name": "Upload Knowledge File", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "title", "value": "Product FAQ", "type": "text"}, {"key": "description", "value": "Frequently asked questions about our products", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/knowledge", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge"]}}}, {"name": "Delete Knowledge File", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/knowledge/1", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge", "1"]}}}]}, {"name": "Meta Token Management", "item": [{"name": "<PERSON> <PERSON>a <PERSON>", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/meta-token/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "meta-token", "{{user_id}}"]}}}, {"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"access_token\": \"your_meta_access_token\",\n  \"page_id\": \"your_facebook_page_id\",\n  \"app_id\": \"your_facebook_app_id\",\n  \"app_secret\": \"your_facebook_app_secret\",\n  \"webhook_verify_token\": \"your_webhook_verify_token\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/meta-token", "host": ["{{base_url}}"], "path": ["api", "v1", "meta-token"]}}}, {"name": "Update <PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"access_token\": \"updated_meta_access_token\",\n  \"page_id\": \"updated_facebook_page_id\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/meta-token/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "meta-token", "{{user_id}}"]}}}]}]}