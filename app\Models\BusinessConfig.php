<?php

declare (strict_types = 1);

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

final class BusinessConfig extends Model
{
    protected $fillable = [
        'key',
        'category',
        'value',
        'description',
        'is_active',
    ];

    protected $casts = [
        'value'     => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get configuration by key
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        $config = static::where('key', $key)
            ->where('is_active', true)
            ->first();

        return $config ? $config->value : $default;
    }

    /**
     * Set configuration value
     */
    public static function set(string $key, mixed $value, string $category = 'general', ?string $description = null): self
    {
        return static::updateOrCreate(
            ['key' => $key],
            [
                'value'       => $value,
                'category'    => $category,
                'description' => $description,
                'is_active'   => true,
            ]
        );
    }

    /**
     * Get all configurations by category
     */
    public static function getByCategory(string $category): Collection
    {
        return static::where('category', $category)
            ->where('is_active', true)
            ->get()
            ->keyBy('key')
            ->map(fn($config) => $config->value);
    }

    // =============================================================================
    // AGENT PROFILE METHODS
    // =============================================================================

    /**
     * Get agent profile configuration
     */
    public static function getAgentProfile(): array
    {
        return static::get('agent_profile', []);
    }

    /**
     * Get agent name
     */
    public static function getAgentName(): string
    {
        $profile = static::getAgentProfile();
        return $profile['agent_name'] ?? 'Assistant';
    }

    /**
     * Get shop UID
     */
    public static function getShopUID(): string
    {
        $profile = static::getAgentProfile();
        return $profile['shop_uid'] ?? 'SHOP-' . strtoupper(substr(md5(uniqid()), 0, 8));
    }

    /**
     * Get agent type
     */
    public static function getAgentType(): string
    {
        $profile = static::getAgentProfile();
        return $profile['agent_type'] ?? 'Customer Service';
    }

    /**
     * Get shop type
     */
    public static function getShopType(): string
    {
        $profile = static::getAgentProfile();
        return $profile['shop_type'] ?? 'Service Business';
    }

    // =============================================================================
    // INTEGRATION CHANNELS METHODS
    // =============================================================================

    /**
     * Get integration channels configuration
     */
    public static function getIntegrationChannels(): array
    {
        return static::get('integration_channels', []);
    }

    /**
     * Check if a specific channel is enabled
     */
    public static function isChannelEnabled(string $channel): bool
    {
        $channels = static::getIntegrationChannels();
        return $channels[$channel]['enabled'] ?? false;
    }

    /**
     * Get enabled channels
     */
    public static function getEnabledChannels(): array
    {
        $channels = static::getIntegrationChannels();
        return array_filter($channels, fn($channel) => $channel['enabled'] ?? false);
    }

    // =============================================================================
    // ENHANCED SCHEDULING METHODS
    // =============================================================================

    /**
     * Get business hours for a specific day
     */
    public static function getBusinessHours(string $day = null): ?array
    {
        $day           = $day ?? strtolower(Carbon::now()->format('l'));
        $businessHours = static::get('business_hours', []);

        return $businessHours[$day] ?? null;
    }

    /**
     * Get time slots configuration
     */
    public static function getTimeSlotsConfig(): array
    {
        return static::get('time_slots_config', [
            'global_time_periods'   => [
                'morning'   => ['09:00', '12:00'],
                'afternoon' => ['13:00', '17:00'],
                'evening'   => ['17:00', '20:00'],
            ],
            'slot_duration_minutes' => 60,
            'buffer_minutes'        => 15,
            'max_advance_days'      => 30,
            'min_advance_hours'     => 2,
        ]);
    }

    /**
     * Get global time periods
     */
    public static function getGlobalTimePeriods(): array
    {
        $config = static::getTimeSlotsConfig();
        return $config['global_time_periods'] ?? [];
    }

    /**
     * Get weekend settings
     */
    public static function getWeekendSettings(): array
    {
        return static::get('weekend_settings', [
            'saturday_enabled' => true,
            'sunday_enabled'   => false,
            'weekend_hours'    => [
                'saturday' => ['09:00', '16:00'],
                'sunday'   => null,
            ],
        ]);
    }

    /**
     * Check if weekends are enabled
     */
    public static function isWeekendEnabled(string $day): bool
    {
        $settings = static::getWeekendSettings();
        return $settings[strtolower($day) . '_enabled'] ?? false;
    }

    /**
     * Get unavailable dates
     */
    public static function getUnavailableDates(): array
    {
        return static::get('unavailable_dates', []);
    }

    /**
     * Get disabled time slots for specific dates
     */
    public static function getDisabledTimeSlots(): array
    {
        return static::get('disabled_time_slots', []);
    }

    /**
     * Check if a specific date has disabled time slots
     */
    public static function getDisabledSlotsForDate(string $date): array
    {
        $disabledSlots = static::getDisabledTimeSlots();
        return $disabledSlots[$date] ?? [];
    }

    /**
     * Check if business is open on a specific date
     */
    public static function isBusinessOpen(Carbon $date): bool
    {
        $dayName = strtolower($date->format('l'));

        // Check regular business hours
        $businessHours = static::getBusinessHours($dayName);
        if (!$businessHours) {
            // Check weekend settings
            $weekendSettings = static::getWeekendSettings();
            if (in_array($dayName, ['saturday', 'sunday'])) {
                $businessHours = $weekendSettings['weekend_hours'][$dayName] ?? null;
            }
        }

        if (!$businessHours) {
            return false;
        }

        // Check unavailable dates
        $unavailableDates = static::getUnavailableDates();
        $dateString       = $date->format('Y-m-d');

        // Check holidays
        if (in_array($dateString, $unavailableDates['holidays'] ?? [])) {
            return false;
        }

        // Check maintenance days
        if (in_array($dateString, $unavailableDates['maintenance_days'] ?? [])) {
            return false;
        }

        // Check vacation periods
        foreach ($unavailableDates['vacation_periods'] ?? [] as $period) {
            if ($dateString >= $period['start'] && $dateString <= $period['end']) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get available time slots for a specific date with enhanced features
     */
    public static function getAvailableSlots(Carbon $date): array
    {
        if (!static::isBusinessOpen($date)) {
            return [];
        }

        $dayName       = strtolower($date->format('l'));
        $businessHours = static::getBusinessHours($dayName);

        // Check weekend hours if regular hours not available
        if (!$businessHours) {
            $weekendSettings = static::getWeekendSettings();
            $businessHours   = $weekendSettings['weekend_hours'][$dayName] ?? null;
        }

        if (!$businessHours) {
            return [];
        }

        $slotConfig      = static::getTimeSlotsConfig();
        $timePeriods     = $slotConfig['global_time_periods'] ?? [];
        $slotDuration    = $slotConfig['slot_duration_minutes'] ?? 60;
        $bufferMinutes   = $slotConfig['buffer_minutes'] ?? 15;
        $minAdvanceHours = $slotConfig['min_advance_hours'] ?? 2;

        $slots         = [];
        $dateString    = $date->format('Y-m-d');
        $disabledSlots = static::getDisabledSlotsForDate($dateString);

        // Generate slots for each time period
        foreach ($timePeriods as $periodName => $periodHours) {
            $periodStart = Carbon::createFromFormat('H:i', $periodHours[0]);
            $periodEnd   = Carbon::createFromFormat('H:i', $periodHours[1]);

            // Ensure period is within business hours
            $businessStart = Carbon::createFromFormat('H:i', $businessHours[0]);
            $businessEnd   = Carbon::createFromFormat('H:i', $businessHours[1]);

            if ($periodStart->lt($businessStart)) {
                $periodStart = $businessStart;
            }
            if ($periodEnd->gt($businessEnd)) {
                $periodEnd = $businessEnd;
            }

            $currentSlot = $periodStart->copy();

            while ($currentSlot->copy()->addMinutes($slotDuration)->lte($periodEnd)) {
                $slotTime = $currentSlot->format('H:i');

                // Skip if slot is disabled for this date
                if (in_array($slotTime, $disabledSlots)) {
                    $currentSlot->addMinutes($slotDuration + $bufferMinutes);
                    continue;
                }

                // Check if slot is in the future (for today)
                if ($date->isToday()) {
                    $slotDateTime = $date->copy()->setTimeFromTimeString($currentSlot->format('H:i:s'));
                    if ($slotDateTime->lt(Carbon::now()->addHours($minAdvanceHours))) {
                        $currentSlot->addMinutes($slotDuration + $bufferMinutes);
                        continue;
                    }
                }

                $slots[] = [
                    'time'      => $slotTime,
                    'formatted' => $currentSlot->format('g:i A'),
                    'datetime'  => $date->copy()->setTimeFromTimeString($currentSlot->format('H:i:s')),
                    'period'    => $periodName,
                ];

                $currentSlot->addMinutes($slotDuration + $bufferMinutes);
            }
        }

        return $slots;
    }

    /**
     * Get services with pricing
     */
    public static function getServices(): array
    {
        return static::get('services', []);
    }

    /**
     * Get service by key
     */
    public static function getService(string $serviceKey): ?array
    {
        $services = static::getServices();
        return $services[$serviceKey] ?? null;
    }

    /**
     * Get services by category
     */
    public static function getServicesByCategory(string $category): array
    {
        $services = static::getServices();
        return array_filter($services, fn($service) => ($service['category'] ?? '') === $category);
    }

    /**
     * Get chatbot prompts
     */
    public static function getChatbotPrompts(): array
    {
        return static::get('chatbot_prompts', []);
    }

    /**
     * Get specific chatbot prompt
     */
    public static function getChatbotPrompt(string $key, string $default = ''): string
    {
        $prompts = static::getChatbotPrompts();
        return $prompts[$key] ?? $default;
    }

    /**
     * Get next available appointment slots with enhanced features
     */
    public static function getNextAvailableSlots(int $days = 7): array
    {
        $availableSlots = [];
        $startDate      = Carbon::now()->startOfDay();
        $config         = static::getTimeSlotsConfig();
        $maxAdvanceDays = $config['max_advance_days'] ?? 30;

        // Don't exceed max advance days
        $days = min($days, $maxAdvanceDays);

        for ($i = 0; $i < $days; $i++) {
            $checkDate = $startDate->copy()->addDays($i);
            $slots     = static::getAvailableSlots($checkDate);

            if (!empty($slots)) {
                $dayName         = strtolower($checkDate->format('l'));
                $isWeekend       = in_array($dayName, ['saturday', 'sunday']);
                $weekendSettings = static::getWeekendSettings();

                $availableSlots[$checkDate->format('Y-m-d')] = [
                    'date'               => $checkDate->format('Y-m-d'),
                    'formatted_date'     => $checkDate->format('l, M j'),
                    'is_weekend'         => $isWeekend,
                    'pricing_multiplier' => $isWeekend ? ($weekendSettings['weekend_pricing_multiplier'] ?? 1.0) : 1.0,
                    'slots'              => $slots,
                ];
            }
        }

        return $availableSlots;
    }

    /**
     * Check if a specific time slot is available
     */
    public static function isSlotAvailable(Carbon $datetime): bool
    {
        $date = $datetime->copy()->startOfDay();
        $time = $datetime->format('H:i');

        if (!static::isBusinessOpen($date)) {
            return false;
        }

        $availableSlots = static::getAvailableSlots($date);
        foreach ($availableSlots as $slot) {
            if ($slot['time'] === $time) {
                return true;
            }
        }

        return false;
    }

    /**
     * Disable a specific time slot for a specific date
     */
    public static function disableTimeSlot(string $date, string $time): bool
    {
        $disabledSlots = static::getDisabledTimeSlots();

        if (!isset($disabledSlots[$date])) {
            $disabledSlots[$date] = [];
        }

        if (!in_array($time, $disabledSlots[$date])) {
            $disabledSlots[$date][] = $time;
            static::set('disabled_time_slots', $disabledSlots, 'scheduling');
            return true;
        }

        return false;
    }

    /**
     * Enable a specific time slot for a specific date
     */
    public static function enableTimeSlot(string $date, string $time): bool
    {
        $disabledSlots = static::getDisabledTimeSlots();

        if (isset($disabledSlots[$date])) {
            $key = array_search($time, $disabledSlots[$date]);
            if ($key !== false) {
                unset($disabledSlots[$date][$key]);
                $disabledSlots[$date] = array_values($disabledSlots[$date]);

                // Remove date key if no more disabled slots
                if (empty($disabledSlots[$date])) {
                    unset($disabledSlots[$date]);
                }

                static::set('disabled_time_slots', $disabledSlots, 'scheduling');
                return true;
            }
        }

        return false;
    }

    /**
     * Add unavailable date
     */
    public static function addUnavailableDate(string $date, string $type = 'holidays'): bool
    {
        $unavailableDates = static::getUnavailableDates();

        if (!isset($unavailableDates[$type])) {
            $unavailableDates[$type] = [];
        }

        if (!in_array($date, $unavailableDates[$type])) {
            $unavailableDates[$type][] = $date;
            static::set('unavailable_dates', $unavailableDates, 'scheduling');
            return true;
        }

        return false;
    }

    /**
     * Remove unavailable date
     */
    public static function removeUnavailableDate(string $date, string $type = 'holidays'): bool
    {
        $unavailableDates = static::getUnavailableDates();

        if (isset($unavailableDates[$type])) {
            $key = array_search($date, $unavailableDates[$type]);
            if ($key !== false) {
                unset($unavailableDates[$type][$key]);
                $unavailableDates[$type] = array_values($unavailableDates[$type]);
                static::set('unavailable_dates', $unavailableDates, 'scheduling');
                return true;
            }
        }

        return false;
    }

    /**
     * Update configuration by key
     */
    public static function updateConfig(string $key, mixed $value): bool
    {
        $config = static::where('key', $key)->first();
        if ($config) {
            $config->update(['value' => $value]);
            return true;
        }
        return false;
    }

    // =============================================================================
    // SCOPES
    // =============================================================================

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCategory($query, string $category)
    {
        return $query->where('category', $category);
    }
}
