<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enable pgvector extension
        DB::statement('CREATE EXTENSION IF NOT EXISTS vector');

        Schema::create('knowledge_chunks', function (Blueprint $table) {
            $table->id();
            $table->text('content');
            $table->unsignedBigInteger('file_id');
            $table->integer('chunk_index');
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->foreign('file_id')->references('id')->on('knowledge_files')->onDelete('cascade');
            $table->index(['file_id', 'chunk_index']);
        });

        // Add vector column using raw SQL
        DB::statement('ALTER TABLE knowledge_chunks ADD COLUMN embedding vector(1536)');

        // Create index for vector similarity search
        DB::statement('CREATE INDEX knowledge_chunks_embedding_idx ON knowledge_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('knowledge_chunks');
    }
};
