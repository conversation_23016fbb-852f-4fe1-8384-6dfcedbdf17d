<?php

declare (strict_types = 1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class MetaTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only allow admins or authorized users to manage Meta tokens
        return true; // Add proper authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'access_token' => [
                'required',
                'string',
                'min:50',
                'max:1000',
                'regex:/^[A-Za-z0-9_-]+$/',
            ],
            'page_id'      => [
                'required',
                'string',
                'max:255',
                'regex:/^\d+$/',
            ],
            'page_name'    => ['sometimes', 'string', 'max:255'],
            'expires_at'   => ['sometimes', 'date', 'after:now'],
            'scopes'       => ['sometimes', 'array'],
            'scopes.*'     => ['string', 'max:100'],
        ];

        // For updates, make fields optional
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $rules['access_token'][0] = 'sometimes';
            $rules['page_id'][0]      = 'sometimes';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'access_token.required' => 'Access token is required.',
            'access_token.min'      => 'Access token must be at least 50 characters long.',
            'access_token.max'      => 'Access token cannot exceed 1000 characters.',
            'access_token.regex'    => 'Access token contains invalid characters. Only alphanumeric, underscore, and hyphen are allowed.',
            'page_id.required'      => 'Page ID is required.',
            'page_id.regex'         => 'Page ID must contain only numbers.',
            'page_name.max'         => 'Page name cannot exceed 255 characters.',
            'expires_at.date'       => 'Expiration date must be a valid date.',
            'expires_at.after'      => 'Expiration date must be in the future.',
            'scopes.array'          => 'Scopes must be an array.',
            'scopes.*.string'       => 'Each scope must be a string.',
            'scopes.*.max'          => 'Each scope cannot exceed 100 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'access_token' => 'access token',
            'page_id'      => 'page ID',
            'page_name'    => 'page name',
            'expires_at'   => 'expiration date',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Trim whitespace from token and page_id
        if ($this->has('access_token')) {
            $this->merge([
                'access_token' => trim($this->input('access_token')),
            ]);
        }

        if ($this->has('page_id')) {
            $this->merge([
                'page_id' => trim($this->input('page_id')),
            ]);
        }

        // Set default scopes if not provided
        if (!$this->has('scopes')) {
            $this->merge([
                'scopes' => [
                    'pages_messaging',
                    'pages_show_list',
                    'pages_read_engagement',
                ],
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if page_id already exists (for new tokens)
            if ($this->isMethod('POST') && $this->has('page_id')) {
                $existingToken = \App\Models\MetaToken::where('page_id', $this->page_id)
                    ->where('status', 'active')
                    ->first();

                if ($existingToken) {
                    $validator->errors()->add('page_id', 'An active token for this page already exists.');
                }
            }

            // Validate token format (basic check)
            if ($this->has('access_token')) {
                $token = $this->input('access_token');

                // Check if token looks like a valid Meta access token
                if (!preg_match('/^[A-Za-z0-9_-]{50,}$/', $token)) {
                    $validator->errors()->add('access_token', 'Access token format appears to be invalid.');
                }
            }

            // Validate scopes
            if ($this->has('scopes')) {
                $scopes      = $this->input('scopes');
                $validScopes = [
                    'pages_messaging',
                    'pages_show_list',
                    'pages_read_engagement',
                    'pages_manage_metadata',
                    'instagram_basic',
                    'instagram_manage_messages',
                ];

                foreach ($scopes as $scope) {
                    if (!in_array($scope, $validScopes)) {
                        $validator->errors()->add('scopes', "Invalid scope: {$scope}");
                    }
                }
            }
        });
    }

    /**
     * Get the processed token data for the service layer
     */
    public function getTokenData(): array
    {
        $validated = $this->validated();

        return [
            'access_token' => $validated['access_token'],
            'page_id'      => $validated['page_id'],
            'page_name'    => $validated['page_name'] ?? null,
            'expires_at'   => $validated['expires_at'] ?? null,
            'scopes'       => $validated['scopes'] ?? [],
            'status'       => 'active',
        ];
    }

    /**
     * Get masked token for logging/display
     */
    public function getMaskedToken(): string
    {
        if (!$this->has('access_token')) {
            return '';
        }

        $token  = $this->input('access_token');
        $length = strlen($token);

        if ($length <= 8) {
            return str_repeat('*', $length);
        }

        return substr($token, 0, 4) . str_repeat('*', $length - 8) . substr($token, -4);
    }

    /**
     * Validate token with Meta API (optional additional validation)
     */
    public function validateWithMetaAPI(): array
    {
        if (!$this->has('access_token')) {
            return ['valid' => false, 'error' => 'No token provided'];
        }

        try {
            $response = \Illuminate\Support\Facades\Http::timeout(10)
                ->get('https://graph.facebook.com/me', [
                    'access_token' => $this->input('access_token'),
                    'fields'       => 'id,name',
                ]);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'valid'     => true,
                    'page_id'   => $data['id'] ?? null,
                    'page_name' => $data['name'] ?? null,
                ];
            }

            return [
                'valid' => false,
                'error' => 'Token validation failed: ' . $response->body(),
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'error' => 'Token validation error: ' . $e->getMessage(),
            ];
        }
    }
}
