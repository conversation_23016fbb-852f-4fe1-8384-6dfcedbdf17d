<?php

declare (strict_types = 1);

namespace App\Helpers;

final class VectorHelper
{
    /**
     * Calculate cosine distance between two vectors
     */
    public static function cosineDistance(array $vectorA, array $vectorB): float
    {
        if (count($vectorA) !== count($vectorB)) {
            throw new \InvalidArgumentException('Vectors must have the same dimension');
        }

        $dotProduct = 0.0;
        $magnitudeA = 0.0;
        $magnitudeB = 0.0;

        for ($i = 0; $i < count($vectorA); $i++) {
            $dotProduct += $vectorA[$i] * $vectorB[$i];
            $magnitudeA += $vectorA[$i] * $vectorA[$i];
            $magnitudeB += $vectorB[$i] * $vectorB[$i];
        }

        $magnitudeA = sqrt($magnitudeA);
        $magnitudeB = sqrt($magnitudeB);

        if ($magnitudeA == 0.0 || $magnitudeB == 0.0) {
            return 1.0; // Maximum distance
        }

        $cosineSimilarity = $dotProduct / ($magnitudeA * $magnitudeB);

        // Convert similarity to distance (0 = identical, 2 = opposite)
        return 1.0 - $cosineSimilarity;
    }

    /**
     * Calculate cosine similarity between two vectors
     */
    public static function cosineSimilarity(array $vectorA, array $vectorB): float
    {
        return 1.0 - self::cosineDistance($vectorA, $vectorB);
    }

    /**
     * Calculate Euclidean distance between two vectors
     */
    public static function euclideanDistance(array $vectorA, array $vectorB): float
    {
        if (count($vectorA) !== count($vectorB)) {
            throw new \InvalidArgumentException('Vectors must have the same dimension');
        }

        $sum = 0.0;
        for ($i = 0; $i < count($vectorA); $i++) {
            $diff = $vectorA[$i] - $vectorB[$i];
            $sum += $diff * $diff;
        }

        return sqrt($sum);
    }

    /**
     * Calculate dot product of two vectors
     */
    public static function dotProduct(array $vectorA, array $vectorB): float
    {
        if (count($vectorA) !== count($vectorB)) {
            throw new \InvalidArgumentException('Vectors must have the same dimension');
        }

        $product = 0.0;
        for ($i = 0; $i < count($vectorA); $i++) {
            $product += $vectorA[$i] * $vectorB[$i];
        }

        return $product;
    }

    /**
     * Calculate magnitude (length) of a vector
     */
    public static function magnitude(array $vector): float
    {
        $sum = 0.0;
        foreach ($vector as $component) {
            $sum += $component * $component;
        }

        return sqrt($sum);
    }

    /**
     * Normalize a vector to unit length
     */
    public static function normalize(array $vector): array
    {
        $magnitude = self::magnitude($vector);

        if ($magnitude == 0.0) {
            return $vector; // Cannot normalize zero vector
        }

        return array_map(function ($component) use ($magnitude) {
            return $component / $magnitude;
        }, $vector);
    }

    /**
     * Convert vector array to pgvector string format
     */
    public static function arrayToVector(array $vector): string
    {
        return '[' . implode(',', $vector) . ']';
    }

    /**
     * Convert pgvector string format to array
     */
    public static function vectorToArray(string $vector): array
    {
        $cleaned = trim($vector, '[]');
        return array_map('floatval', explode(',', $cleaned));
    }

    /**
     * Find the most similar vectors using cosine similarity
     */
    public static function findMostSimilar(array $targetVector, array $candidates, int $limit = 5): array
    {
        $similarities = [];

        foreach ($candidates as $index => $candidateVector) {
            $similarity     = self::cosineSimilarity($targetVector, $candidateVector);
            $similarities[] = [
                'index'      => $index,
                'similarity' => $similarity,
                'distance'   => 1.0 - $similarity,
                'vector'     => $candidateVector,
            ];
        }

        // Sort by similarity (highest first)
        usort($similarities, function ($a, $b) {
            return $b['similarity'] <=> $a['similarity'];
        });

        return array_slice($similarities, 0, $limit);
    }

    /**
     * Check if two vectors are similar within a threshold
     */
    public static function isSimilar(array $vectorA, array $vectorB, float $threshold = 0.8): bool
    {
        return self::cosineSimilarity($vectorA, $vectorB) >= $threshold;
    }

    /**
     * Calculate the centroid (average) of multiple vectors
     */
    public static function centroid(array $vectors): array
    {
        if (empty($vectors)) {
            throw new \InvalidArgumentException('Cannot calculate centroid of empty vector set');
        }

        $dimensions = count($vectors[0]);
        $centroid   = array_fill(0, $dimensions, 0.0);

        foreach ($vectors as $vector) {
            if (count($vector) !== $dimensions) {
                throw new \InvalidArgumentException('All vectors must have the same dimension');
            }

            for ($i = 0; $i < $dimensions; $i++) {
                $centroid[$i] += $vector[$i];
            }
        }

        $vectorCount = count($vectors);
        for ($i = 0; $i < $dimensions; $i++) {
            $centroid[$i] /= $vectorCount;
        }

        return $centroid;
    }
}
