# Kortana AI Instagram Chatbot

A Laravel-based Instagram chatbot that uses AI to respond to customer messages, provide information about services, and book appointments.

## Features

-   Instagram messaging integration
-   AI-powered responses using Gemini API or local Ollama models
-   Knowledge base with vector search
-   Appointment scheduling
-   Business information management

## Requirements

-   PHP 8.4+
-   Laravel 12.0+
-   PostgreSQL 14+ with pgvector extension
-   Redis (optional, for caching)
-   Google Cloud Gemini API key (or local Ollama installation)
-   Meta Developer Account with Instagram Messaging permissions

## Installation

1. Clone the repository:

    ```bash
    git clone https://github.com/yourusername/kortana-ai.git
    cd kortana-ai
    ```

2. Install dependencies:

    ```bash
    composer install
    ```

3. Copy the environment file:

    ```bash
    cp .env.example .env
    ```

4. Generate application key:

    ```bash
    php artisan key:generate
    ```

5. Configure your database in the `.env` file:

    ```
    DB_CONNECTION=pgsql
    DB_HOST=127.0.0.1
    DB_PORT=5432
    DB_DATABASE=kortana_ai
    DB_USERNAME=postgres
    DB_PASSWORD=your_password
    ```

6. Run migrations:

    ```bash
    php artisan migrate
    ```

7. Seed the database:
    ```bash
    php artisan db:seed
    ```

## AI Configuration

### Option 1: Gemini API (Cloud-based)

1. Obtain a Gemini API key from Google Cloud Console
2. Add it to your `.env` file:
    ```
    GEMINI_API_KEY=your_gemini_api_key_here
    GEMINI_MODEL=gemini-2.0-flash
    GEMINI_EMBEDDING_MODEL=text-embedding-004
    ```

### Option 2: Ollama (Local LLM)

1. Install Ollama from [https://ollama.ai/](https://ollama.ai/)
2. Pull the required models:
    ```bash
    ollama pull deepseek-r1:8b
    ollama pull nomic-embed-text
    ```
3. Start the Ollama service:
    ```bash
    ollama serve
    ```
4. Configure your `.env` file:
    ```
    OLLAMA_ENABLED=true
    OLLAMA_API_URL=http://localhost:11434
    OLLAMA_MODEL=deepseek-r1:8b
    OLLAMA_EMBEDDING_MODEL=nomic-embed-text
    ```

To use Ollama exclusively and disable Gemini API completely:

```
OLLAMA_ENABLED=true
GEMINI_ENABLED=false
```

The application will automatically fall back to Gemini if Ollama is not available or encounters an error, unless Gemini is explicitly set to disabled by setting GEMINI_ENABLED=false.

## Instagram Setup

1. Create a Facebook Developer Account
2. Create a new app with Instagram Messaging permissions
3. Configure the webhook for your app
4. Generate a page access token
5. Update your `.env` file with the required values

For detailed instructions, see the [INSTAGRAM_SETUP_WORKFLOW.md](INSTAGRAM_SETUP_WORKFLOW.md) file.

## Usage

1. Start the Laravel development server:

    ```bash
    php artisan serve
    ```

2. Expose your local server using a tool like ngrok:

    ```bash
    ngrok http 8000
    ```

3. Configure the webhook URL in your Facebook Developer account to point to:

    ```
    https://your-ngrok-url.ngrok.io/api/instagram/webhook
    ```

4. Start receiving and responding to Instagram messages!

## Documentation

-   [API Documentation](API_DOCUMENTATION.md)
-   [Environment Setup](ENVIRONMENT_SETUP.md)
-   [Business Customization Guide](BUSINESS_CUSTOMIZATION_GUIDE.md)
-   [Instagram Setup Workflow](INSTAGRAM_SETUP_WORKFLOW.md)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
