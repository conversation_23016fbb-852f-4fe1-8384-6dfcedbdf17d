<?php

declare (strict_types = 1);

namespace App\Repositories;

use App\Models\KnowledgeChunk;
use App\Models\KnowledgeFile;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

final class KnowledgeRepository
{
    public function __construct(
        private readonly KnowledgeFile $fileModel,
        private readonly KnowledgeChunk $chunkModel
    ) {}

    // File operations
    public function createFile(array $data): KnowledgeFile
    {
        return $this->fileModel->create($data);
    }

    public function findFileById(int $id): ?KnowledgeFile
    {
        return $this->fileModel->find($id);
    }

    public function updateFile(KnowledgeFile $file, array $data): KnowledgeFile
    {
        $file->update($data);
        return $file->fresh();
    }

    public function deleteFile(KnowledgeFile $file): bool
    {
        return $file->delete();
    }

    public function getCompletedFiles(): Collection
    {
        return $this->fileModel->completed()->get();
    }

    public function getFilesByType(string $type): Collection
    {
        return $this->fileModel->byType($type)->get();
    }

    public function getAllFiles(int $perPage = 15): LengthAwarePaginator
    {
        return $this->fileModel->orderBy('created_at', 'desc')->paginate($perPage);
    }

    // Chunk operations
    public function createChunk(array $data): KnowledgeChunk
    {
        return $this->chunkModel->create($data);
    }

    public function createChunks(array $chunks): Collection
    {
        $results = collect();
        foreach ($chunks as $chunkData) {
            $results->push($this->createChunk($chunkData));
        }
        return $results;
    }

    public function findChunkById(int $id): ?KnowledgeChunk
    {
        return $this->chunkModel->find($id);
    }

    public function getChunksForFile(int $fileId): Collection
    {
        return $this->chunkModel->forFile($fileId)->get();
    }

    /**
     * Find similar chunks using vector similarity search
     */
    public function findSimilarChunks(array $embedding, int $limit = 5): Collection
    {
        return $this->chunkModel->similarTo($embedding, $limit)->get();
    }

    /**
     * Find similar chunks with minimum similarity threshold
     */
    public function findSimilarChunksWithThreshold(
        array $embedding,
        float $threshold = 0.8,
        int $limit = 5
    ): Collection {
        return $this->chunkModel->similarTo($embedding, $limit)
            ->having('distance', '<=', $threshold)
            ->get();
    }

    /**
     * Get all chunks with their files for context
     */
    public function getChunksWithFiles(): Collection
    {
        return $this->chunkModel->with('knowledgeFile')->get();
    }

    /**
     * Delete all chunks for a specific file
     */
    public function deleteChunksForFile(int $fileId): bool
    {
        return $this->chunkModel->where('file_id', $fileId)->delete();
    }

    /**
     * Update chunk embedding
     */
    public function updateChunkEmbedding(KnowledgeChunk $chunk, array $embedding): KnowledgeChunk
    {
        $chunk->update(['embedding' => $embedding]);
        return $chunk->fresh();
    }

    /**
     * Get chunk count for a file
     */
    public function getChunkCountForFile(int $fileId): int
    {
        return $this->chunkModel->where('file_id', $fileId)->count();
    }

    /**
     * Mark file processing as completed
     */
    public function markFileAsCompleted(KnowledgeFile $file): KnowledgeFile
    {
        return $this->updateFile($file, ['status' => 'completed']);
    }

    /**
     * Mark file processing as failed
     */
    public function markFileAsFailed(KnowledgeFile $file, string $errorMessage): KnowledgeFile
    {
        return $this->updateFile($file, [
            'status'        => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Get knowledge base statistics
     */
    public function getKnowledgeStats(): array
    {
        return [
            'total_files'      => $this->fileModel->count(),
            'completed_files'  => $this->fileModel->where('status', 'completed')->count(),
            'processing_files' => $this->fileModel->where('status', 'processing')->count(),
            'failed_files'     => $this->fileModel->where('status', 'failed')->count(),
            'total_chunks'     => $this->chunkModel->count(),
            'files_by_type'    => $this->fileModel->groupBy('type')
                ->selectRaw('type, count(*) as count')
                ->pluck('count', 'type')
                ->toArray(),
        ];
    }
}