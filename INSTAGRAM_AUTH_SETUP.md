# Instagram Authentication Setup Guide

This guide explains how to set up Instagram business login for your Kortana AI chatbot using the OAuth flow.

## Overview

The Instagram authentication system allows your application to:

-   Obtain Instagram User Access Tokens with proper permissions
-   Manage multiple Instagram accounts
-   Automatically refresh tokens
-   Handle authentication errors gracefully

## Prerequisites

1. **Meta Developer Account** with your app configured
2. **Instagram Business Account** connected to a Facebook Page
3. **App in Live Mode** (required for messaging)
4. **Proper permissions** configured in Meta Developer Console

## Configuration

### 1. Environment Variables

Add these variables to your `.env` file:

```env
# Instagram OAuth Configuration
INSTAGRAM_APP_ID=****************
INSTAGRAM_APP_SECRET=your_instagram_app_secret_here
INSTAGRAM_REDIRECT_URI=https://kortana-ai.loca.lt/auth/instagram/callback
INSTAGRAM_GRAPH_API_URL=https://graph.instagram.com
```

### 2. Meta Developer Console Setup

1. **Navigate to Instagram Business API Setup:**

    ```
    https://developers.facebook.com/apps/****************/instagram-business/API-Setup/
    ```

2. **Set Redirect URI in Business Login:**

    - Click "Set up" in section 3 (Set up Instagram business login)
    - Enter redirect URI: `https://kortana-ai.loca.lt/auth/instagram/callback`
    - Save the configuration

3. **Switch to Live Mode:**
    - Toggle the app mode from "Development" to "Live"
    - This is **required** for messaging functionality

## API Endpoints

### Authentication Flow

#### 1. Start Authentication

```
GET /auth/instagram/
```

**Response:**

```json
{
    "success": true,
    "data": {
        "auth_url": "https://api.instagram.com/oauth/authorize?client_id=...",
        "state": "random_security_token"
    },
    "message": "Instagram authorization URL generated successfully"
}
```

#### 2. Handle Callback (Automatic)

```
GET /auth/instagram/callback?code=...&state=...
```

-   This endpoint is called automatically by Instagram after user authorization
-   Returns a web page with authentication status
-   Stores the access token in the database

#### 3. Check Connection Status

```
GET /auth/instagram/status
```

**Response (Connected):**

```json
{
    "success": true,
    "data": {
        "connected": true,
        "account": {
            "id": "*****************",
            "username": "kortanaai01",
            "account_type": "BUSINESS",
            "connected_at": "2025-01-15T10:00:00.000000Z",
            "expires_at": "2025-03-15T10:00:00.000000Z"
        }
    }
}
```

#### 4. Disconnect Account

```
POST /auth/instagram/disconnect
```

**Response:**

```json
{
    "success": true,
    "data": {
        "message": "Instagram account disconnected successfully"
    }
}
```

#### 5. Refresh Token

```
POST /auth/instagram/refresh
```

**Response:**

```json
{
    "success": true,
    "data": {
        "message": "Instagram token refreshed successfully",
        "expires_at": "2025-06-15T10:00:00.000000Z"
    }
}
```

## How to Use

### Method 1: Direct Browser Access

1. **Open the authentication URL:**

    ```
    https://kortana-ai.loca.lt/auth/instagram/
    ```

2. **Copy the returned auth_url and open it in your browser**

3. **Authorize the app** in Instagram

4. **You'll be redirected** to the callback page showing the result

### Method 2: API Integration

```javascript
// 1. Get authorization URL
const authResponse = await fetch("/auth/instagram/");
const authData = await authResponse.json();

// 2. Open authorization URL in popup or redirect
window.open(authData.data.auth_url, "instagram-auth", "width=600,height=600");

// 3. Listen for completion (if using popup)
window.addEventListener("message", (event) => {
    if (event.data.type === "instagram_auth_complete") {
        // Authentication completed
        console.log("Auth success:", event.data.success);
    }
});
```

### Method 3: PowerShell Testing

Run the provided test script:

```powershell
.\test_instagram_auth.ps1
```

## Database Schema

The authentication system uses the existing `meta_tokens` table:

```sql
CREATE TABLE meta_tokens (
    id BIGINT PRIMARY KEY,
    platform VARCHAR(255) NOT NULL,           -- 'instagram'
    platform_user_id VARCHAR(255) NOT NULL,   -- Instagram account ID
    access_token TEXT NOT NULL,               -- OAuth access token
    token_type VARCHAR(255) DEFAULT 'bearer',
    expires_at TIMESTAMP NULL,                -- Token expiration
    scope TEXT NULL,                          -- Granted permissions
    username VARCHAR(255) NULL,               -- Instagram username
    account_type VARCHAR(255) NULL,           -- 'BUSINESS', 'CREATOR', etc.
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## Required Permissions

The system requests these Instagram permissions:

-   `instagram_business_basic` - Basic account access
-   `instagram_business_manage_messages` - Send/receive messages
-   `instagram_business_manage_comments` - Manage comments
-   `instagram_business_content_publish` - Publish content
-   `instagram_business_manage_insights` - Access insights

## Error Handling

### Common Errors

1. **"Invalid state parameter"**

    - Session expired or tampered state
    - Solution: Start authentication flow again

2. **"Authorization code not provided"**

    - User canceled authorization or network error
    - Solution: Retry authentication

3. **"Failed to exchange code for access token"**

    - Invalid app credentials or expired code
    - Solution: Check app configuration in Meta Console

4. **"No Instagram token found to refresh"**
    - No active connection exists
    - Solution: Complete authentication flow first

### Error Response Format

```json
{
    "success": false,
    "message": "Error description",
    "error_code": "ERROR_TYPE"
}
```

## Security Features

1. **State Parameter Validation** - Prevents CSRF attacks
2. **Session Management** - Secure state storage
3. **Token Encryption** - Sensitive data protection
4. **Automatic Cleanup** - Expired tokens are deactivated
5. **Scope Validation** - Only requested permissions

## Testing

### Manual Testing

1. Use the PowerShell script: `.\test_instagram_auth.ps1`
2. Check browser developer tools for JavaScript errors
3. Verify database entries in `meta_tokens` table

### Automated Testing

```php
// Feature test example
public function test_instagram_authentication_flow()
{
    $response = $this->get('/auth/instagram/');
    $response->assertStatus(200);
    $response->assertJsonStructure([
        'success',
        'data' => ['auth_url', 'state']
    ]);
}
```

## Troubleshooting

### Issue: "App does not have the capability to make this API call"

**Solution:** Ensure your app is in Live mode and has proper permissions

### Issue: "Invalid OAuth access token"

**Solution:** Regenerate token using the authentication flow

### Issue: "Callback URL not whitelisted"

**Solution:** Add the redirect URI in Meta Developer Console Business Login settings

### Issue: "Token expired"

**Solution:** Use the refresh endpoint or re-authenticate

## Integration with Existing Chatbot

After successful authentication, the token is automatically used by:

-   `InstagramService` for sending messages
-   `ChatbotService` for API calls
-   Webhook processing for incoming messages

The system will automatically use the stored token for all Instagram API operations.

## Next Steps

1. **Complete the authentication flow**
2. **Switch your app to Live mode**
3. **Test message sending functionality**
4. **Monitor token expiration and refresh as needed**

For additional support, check the Laravel logs at `storage/logs/laravel.log` for detailed error information.
