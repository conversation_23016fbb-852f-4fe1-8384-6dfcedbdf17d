<?php

declare (strict_types = 1);

namespace App\Console\Commands;

use App\Models\MetaToken;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class FixInstagramToken extends Command
{
    protected $signature   = 'instagram:refresh-tokens';
    protected $description = 'Refresh Instagram tokens that are expiring soon';

    public function handle(): int
    {
        $this->info('Starting Instagram token refresh process...');

        // Find tokens that are expiring within 7 days but are not yet expired
        $expiringTokens = MetaToken::where('platform', 'instagram')
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNotNull('expires_at')
                    ->where('expires_at', '<=', now()->addDays(7))
                    ->where('expires_at', '>', now());
            })
            ->get();

        $this->info("Found {$expiringTokens->count()} Instagram tokens to refresh");

        if ($expiringTokens->isEmpty()) {
            $this->info('No tokens need refreshing at this time.');
            return Command::SUCCESS;
        }

        $refreshed = 0;
        $failed    = 0;

        foreach ($expiringTokens as $token) {
            $this->info("Processing token ID: {$token->id} for {$token->username}");

            try {
                $response = Http::get('https://graph.instagram.com/refresh_access_token', [
                    'grant_type'   => 'ig_refresh_token',
                    'access_token' => $token->access_token,
                ]);

                if ($response->successful()) {
                    $newTokenData = $response->json();

                    $token->update([
                        'access_token' => $newTokenData['access_token'],
                        'expires_at'   => now()->addSeconds($newTokenData['expires_in']),
                    ]);

                    $this->info("✓ Successfully refreshed token for {$token->username} (ID: {$token->id})");
                    $this->info("  New expiration: {$token->expires_at->format('Y-m-d H:i:s')}");
                    $refreshed++;
                } else {
                    $this->error("✗ Failed to refresh token ID {$token->id}: " . $response->body());
                    Log::error("Instagram token refresh failed for ID {$token->id}", [
                        'status'   => $response->status(),
                        'response' => $response->body(),
                    ]);
                    $failed++;
                }

            } catch (\Exception $e) {
                $this->error("✗ Error refreshing token ID {$token->id}: " . $e->getMessage());
                Log::error("Instagram token refresh exception for ID {$token->id}", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
                $failed++;
            }
        }

        $this->info("Instagram token refresh completed: {$refreshed} refreshed, {$failed} failed");

        return $failed > 0 ? Command::FAILURE : Command::SUCCESS;
    }
}
