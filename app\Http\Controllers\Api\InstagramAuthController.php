<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MetaToken;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

final class InstagramAuthController extends Controller
{
    use ApiResponseTrait;

    private string $clientId;
    private string $clientSecret;
    private string $redirectUri;
    private string $graphApiUrl;

    public function __construct()
    {
        $this->clientId     = config('services.instagram.app_id');
        $this->clientSecret = config('services.instagram.app_secret');
        $this->redirectUri  = config('services.instagram.redirect_uri');
        $this->graphApiUrl  = config('services.instagram.graph_api_url', 'https://graph.instagram.com');
    }

    /**
     * Redirect user to Instagram OAuth authorization page
     */
    public function redirectToInstagram(): JsonResponse
    {
        // Generate a random state parameter for security
        $state = Str::random(40);
        session(['instagram_oauth_state' => $state]);

        // Define scopes for Instagram Business API
        // Using the new scope values as per Meta's documentation (old ones will be deprecated on January 27, 2025)
        $scope = 'instagram_business_basic,instagram_business_manage_messages,instagram_business_manage_comments,instagram_business_content_publish,instagram_business_manage_insights';

        // Instagram OAuth URL
        $authUrl = "https://www.instagram.com/oauth/authorize?" . http_build_query([
            'client_id'     => $this->clientId,
            'redirect_uri'  => $this->redirectUri,
            'scope'         => $scope,
            'response_type' => 'code',
            'state'         => $state,
            'force_reauth'  => 'true',
        ]);

        return $this->successResponse([
            'auth_url' => $authUrl,
            'state'    => $state,
        ], 'Instagram authorization URL generated successfully');
    }

    /**
     * Handle Instagram OAuth callback
     */
    public function handleCallback(Request $request)
    {
        // If this is an AJAX request or API call, return JSON
        if ($request->expectsJson() || $request->is('api/*')) {
            return $this->handleCallbackJson($request);
        }

        // For web requests, return the view and let JavaScript handle the API call
        return view('instagram-auth');
    }

    /**
     * Handle Instagram OAuth callback and return JSON response
     */
    public function handleCallbackJson(Request $request): JsonResponse
    {
        try {
            // Validate required parameters
            $code             = $request->query('code');
            $state            = $request->query('state');
            $error            = $request->query('error');
            $errorReason      = $request->query('error_reason');
            $errorDescription = $request->query('error_description');

            Log::info('Instagram callback received', [
                'code_exists'       => !empty($code),
                'code_length'       => !empty($code) ? strlen($code) : 0,
                'state'             => $state,
                'error'             => $error,
                'error_reason'      => $errorReason,
                'error_description' => $errorDescription,
                'full_url'          => $request->fullUrl(),
            ]);

            // Check for OAuth errors
            if ($error) {
                Log::error('Instagram OAuth error', [
                    'error'             => $error,
                    'error_reason'      => $errorReason,
                    'error_description' => $errorDescription,
                ]);

                return $this->errorResponse(
                    "Instagram authorization failed: {$errorDescription}",
                    400
                );
            }

            // Validate state parameter for CSRF protection
            $sessionState = session('instagram_oauth_state');
            if ($state && $sessionState && $state !== $sessionState) {
                Log::error('Instagram OAuth state mismatch', [
                    'received_state' => $state,
                    'session_state'  => $sessionState,
                ]);

                return $this->errorResponse('Invalid state parameter', 400);
            }

            // Clear the state from session
            session()->forget('instagram_oauth_state');

            if (!$code) {
                return $this->errorResponse('Authorization code not provided', 400);
            }

            // Step 1: Exchange code for short-lived access token
            $shortLivedToken = $this->getShortLivedToken($code);

            if (!$shortLivedToken) {
                return $this->errorResponse('Failed to exchange code for access token', 500);
            }

            Log::info('Short-lived token obtained', [
                'token_data'   => array_merge(
                    $shortLivedToken,
                    ['access_token' => substr($shortLivedToken['access_token'] ?? '', 0, 10) . '...']
                ),
                'user_id_type' => isset($shortLivedToken['user_id']) ? gettype($shortLivedToken['user_id']) : 'not set',
            ]);

            // Step 2: Exchange short-lived token for long-lived token
            $longLivedToken = $this->getLongLivedToken($shortLivedToken['access_token']);

            if (!$longLivedToken) {
                return $this->errorResponse('Failed to exchange for long-lived token', 500);
            }

            Log::info('Long-lived token obtained', [
                'token_data' => array_merge(
                    $longLivedToken,
                    ['access_token' => substr($longLivedToken['access_token'] ?? '', 0, 10) . '...']
                ),
            ]);

            // Step 3: Get Instagram account info with extended fields
            $igAccountInfo = $this->getInstagramAccountInfo($longLivedToken['access_token']);

            if (!$igAccountInfo) {
                return $this->errorResponse('Failed to retrieve Instagram account information', 500);
            }

            try {
                // Step 4: Store token in database
                $metaToken = $this->storeToken($longLivedToken, (string) $shortLivedToken['user_id'], $igAccountInfo, null);

                return $this->successResponse([
                    'token_id'     => $metaToken->id,
                    'username'     => $metaToken->username,
                    'account_type' => $metaToken->account_type,
                    'expires_at'   => $metaToken->expires_at,
                    'media_count'  => $metaToken->media_count,
                ], 'Instagram account connected successfully');
            } catch (\Exception $dbException) {
                Log::error('Database error during token storage', [
                    'error' => $dbException->getMessage(),
                    'trace' => $dbException->getTraceAsString(),
                ]);

                return $this->errorResponse(
                    'Error storing Instagram token: ' . $dbException->getMessage(),
                    500
                );
            }

        } catch (\Exception $e) {
            Log::error('Instagram OAuth callback error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'An error occurred during Instagram authorization: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Exchange authorization code for short-lived access token
     */
    private function getShortLivedToken(string $code): ?array
    {
        try {
            Log::info('Exchanging code for token', [
                'code_length'  => strlen($code),
                'redirect_uri' => $this->redirectUri,
            ]);

            $response = Http::asForm()->post('https://api.instagram.com/oauth/access_token', [
                'client_id'     => $this->clientId,
                'client_secret' => $this->clientSecret,
                'grant_type'    => 'authorization_code',
                'redirect_uri'  => $this->redirectUri,
                'code'          => $code,
            ]);

            if ($response->successful()) {
                $responseData = $response->json();
                Log::info('Instagram token exchange response', [
                    'response'            => $responseData,
                    'user_id_type'        => isset($responseData['user_id']) ? gettype($responseData['user_id']) : 'not set',
                    'access_token_length' => isset($responseData['access_token']) ? strlen($responseData['access_token']) : 0,
                ]);

                // Mark this as a short-lived token
                $responseData['token_source'] = 'short_lived';

                return $responseData;
            }

            Log::error('Instagram token exchange failed', [
                'status'   => $response->status(),
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Instagram token exchange exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Exchange short-lived token for long-lived token
     * Valid for 60 days
     */
    private function getLongLivedToken(string $shortLivedToken): ?array
    {
        try {
            $response = Http::get("{$this->graphApiUrl}/access_token", [
                'grant_type'    => 'ig_exchange_token',
                'client_secret' => $this->clientSecret,
                'access_token'  => $shortLivedToken,
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                // Mark this as a long-lived token
                $responseData['token_source'] = 'long_lived';

                return $responseData;
            }

            Log::error('Long-lived token exchange failed', [
                'status'   => $response->status(),
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Long-lived token exchange exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Get Instagram account information with extended fields
     */
    private function getInstagramAccountInfo(string $accessToken): ?array
    {
        try {
            // Request only fields that are in our schema
            $fields = 'id,user_id,username,account_type,media_count,profile_picture_url,name,followers_count,follows_count';

            $response = Http::get("{$this->graphApiUrl}/me", [
                'fields'       => $fields,
                'access_token' => $accessToken,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                Log::info('Instagram account info retrieved', [
                    'data' => $data,
                ]);

                // Check if token has business permissions
                $permissionsResponse = Http::get("{$this->graphApiUrl}/me/permissions", [
                    'access_token' => $accessToken,
                ]);

                if ($permissionsResponse->successful()) {
                    $permissions         = $permissionsResponse->json();
                    $data['permissions'] = $permissions['data'] ?? [];

                    // Check if it has instagram_business_basic permission
                    $data['has_business_permission'] = false;
                    if (isset($permissions['data']) && is_array($permissions['data'])) {
                        foreach ($permissions['data'] as $permission) {
                            if (($permission['permission'] === 'instagram_business_basic' ||
                                $permission['permission'] === 'business_basic') &&
                                $permission['status'] === 'granted') {
                                $data['has_business_permission'] = true;
                                break;
                            }
                        }
                    }
                }

                return $data;
            }

            Log::error('Instagram account info request failed', [
                'status'   => $response->status(),
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Instagram account info exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Store Instagram token in database
     */
    private function storeToken(array $tokenData, $userId, ?array $igAccountInfo = null, ?array $pageData = null): MetaToken
    {
        // Ensure userId is a string
        $userId = (string) $userId;

        // Make sure we have a valid Instagram ID
        $instagramId = null;

        // First try to get the ID from the account info
        if ($igAccountInfo && isset($igAccountInfo['id'])) {
            $instagramId = (string) $igAccountInfo['id'];
        }
        // If not available, use the userId as fallback
        else {
            $instagramId = $userId;
        }

        // Log the ID we're using
        Log::info('Using Instagram app-scoped ID for token', [
            'app_scope_id' => $instagramId,
            'source'       => $igAccountInfo && isset($igAccountInfo['id']) ? 'account_info' : 'user_id',
        ]);

        // Deactivate any existing tokens for this Instagram account
        MetaToken::where('platform', 'instagram')
            ->where(function ($query) use ($instagramId, $igAccountInfo) {
                $query->where('app_scope_id', $instagramId);
                if ($igAccountInfo && isset($igAccountInfo['user_id'])) {
                    $query->orWhere('user_id', (string) $igAccountInfo['user_id']);
                }
            })
            ->update(['is_active' => false]);

        // Calculate expiration date from expires_in (seconds)
        $expiresAt = null;
        if (isset($tokenData['expires_in'])) {
            $expiresAt = now()->addSeconds($tokenData['expires_in']);
        }

        // Determine permissions granted
        $scopes = [];
        if (isset($igAccountInfo['permissions']) && is_array($igAccountInfo['permissions'])) {
            $scopes = array_map(function ($item) {
                return $item['permission'] ?? '';
            }, $igAccountInfo['permissions']);
        } elseif (isset($tokenData['permissions'])) {
            $scopes = is_array($tokenData['permissions'])
            ? $tokenData['permissions']
            : explode(',', $tokenData['permissions']);
        }

        // Create new token record with fields that match our schema
        return MetaToken::create([
            'platform'                => 'instagram',
            'app_scope_id'            => $instagramId,
            'user_id'                 => $igAccountInfo['user_id'] ?? null,
            'access_token'            => $tokenData['access_token'],
            'token_type'              => $tokenData['token_type'] ?? 'bearer',
            'scopes'                  => $scopes,
            'is_active'               => true,
            'status'                  => 'active',
            'expires_at'              => $expiresAt,
            'page_id'                 => $pageData['page_id'] ?? null,
            'username'                => $igAccountInfo['username'] ?? null,
            'name'                    => $igAccountInfo['name'] ?? null,
            'account_type'            => $igAccountInfo['account_type'] ?? null,
            'media_count'             => $igAccountInfo['media_count'] ?? null,
            'profile_picture_url'     => $igAccountInfo['profile_picture_url'] ?? null,
            'has_business_permission' => $igAccountInfo['has_business_permission'] ?? false,
            'followers_count'         => $igAccountInfo['followers_count'] ?? null,
            'follows_count'           => $igAccountInfo['follows_count'] ?? null,
        ]);
    }

    /**
     * Get current Instagram connection status
     */
    public function getConnectionStatus(): JsonResponse
    {
        try {
            $token = MetaToken::where('platform', 'instagram')
                ->where('is_active', true)
                ->first();

            if (!$token) {
                return $this->successResponse([
                    'connected' => false,
                    'message'   => 'No Instagram account connected',
                ]);
            }

            // Check if token is expired
            if ($token->expires_at && $token->expires_at->isPast()) {
                $token->update(['is_active' => false, 'status' => 'expired']);

                return $this->successResponse([
                    'connected' => false,
                    'message'   => 'Instagram token has expired',
                ]);
            }

            // Check if token needs refreshing (within 7 days of expiration)
            if ($token->needsRefresh()) {
                // Try to refresh the token automatically
                $this->attemptTokenRefresh($token);
                // Reload the token to get updated data
                $token->refresh();
            }

            return $this->successResponse([
                'connected' => true,
                'account'   => [
                    'id'                      => $token->user_id ?? $token->app_scope_id,
                    'username'                => $token->username,
                    'name'                    => $token->name,
                    'account_type'            => $token->account_type,
                    'connected_at'            => $token->created_at,
                    'expires_at'              => $token->expires_at,
                    'days_until_expiration'   => $token->days_until_expiration,
                    'media_count'             => $token->media_count,
                    'followers_count'         => $token->followers_count,
                    'follows_count'           => $token->follows_count,
                    'has_business_permission' => $token->has_business_permission,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram connection status error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Failed to check Instagram connection status',
                500
            );
        }
    }

    /**
     * Disconnect Instagram account
     */
    public function disconnect(): JsonResponse
    {
        try {
            $updated = MetaToken::where('platform', 'instagram')
                ->where('is_active', true)
                ->update(['is_active' => false, 'status' => 'revoked']);

            if ($updated === 0) {
                return $this->errorResponse('No active Instagram connection found', 404);
            }

            Log::info('Instagram account disconnected');

            return $this->successResponse(
                ['message' => 'Instagram account disconnected successfully']
            );

        } catch (\Exception $e) {
            Log::error('Instagram disconnect error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Failed to disconnect Instagram account',
                500
            );
        }
    }

    /**
     * Refresh Instagram access token
     */
    public function refreshToken(): JsonResponse
    {
        try {
            $token = MetaToken::where('platform', 'instagram')
                ->where('is_active', true)
                ->first();

            if (!$token) {
                return $this->errorResponse('No Instagram token found to refresh', 404);
            }

            // Verify token is eligible for refresh
            if (!$token->isEligibleForRefresh()) {
                return $this->errorResponse(
                    'Token is not eligible for refresh. It must be at least 24 hours old, active, not expired, and have business permissions.',
                    400
                );
            }

            $refreshed = $this->attemptTokenRefresh($token);

            if (!$refreshed) {
                return $this->errorResponse('Failed to refresh Instagram token', 500);
            }

            // Reload the token to get updated data
            $token->refresh();

            return $this->successResponse([
                'message'               => 'Instagram token refreshed successfully',
                'expires_at'            => $token->expires_at,
                'days_until_expiration' => $token->days_until_expiration,
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram token refresh error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Failed to refresh Instagram token: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Attempt to refresh a token
     *
     * @param MetaToken $token The token to refresh
     * @return bool True if refresh was successful
     */
    private function attemptTokenRefresh(MetaToken $token): bool
    {
        try {
            // Check if token has instagram_business_basic permission
            if (!$token->hasBusinessPermission()) {
                Log::warning('Token lacks instagram_business_basic permission required for refresh', [
                    'token_id' => $token->id,
                    'scopes'   => $token->scopes,
                ]);
                return false;
            }

            // Instagram Basic Display API tokens can be refreshed
            $response = Http::get("{$this->graphApiUrl}/refresh_access_token", [
                'grant_type'   => 'ig_refresh_token',
                'access_token' => $token->access_token,
            ]);

            if (!$response->successful()) {
                Log::error('Instagram token refresh failed', [
                    'status'   => $response->status(),
                    'response' => $response->body(),
                ]);
                return false;
            }

            $refreshData = $response->json();

            // Update token in database
            $token->update([
                'access_token' => $refreshData['access_token'],
                'expires_at'   => isset($refreshData['expires_in']) ?
                now()->addSeconds($refreshData['expires_in']) : null,
                'updated_at'   => now(),
            ]);

            Log::info('Instagram token refreshed successfully', [
                'token_id'   => $token->id,
                'expires_at' => $token->expires_at,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Token refresh attempt failed', [
                'error'    => $e->getMessage(),
                'token_id' => $token->id,
                'trace'    => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Get active Instagram token
     *
     * @return MetaToken|null The active token or null if none found
     */
    public function getActiveToken(): ?MetaToken
    {
        $token = MetaToken::where('platform', 'instagram')
            ->where('is_active', true)
            ->first();

        if (!$token) {
            return null;
        }

        // Check if token is expired
        if ($token->isExpired()) {
            $token->update(['is_active' => false, 'status' => 'expired']);
            return null;
        }

        // Check if token needs refreshing (within 7 days of expiration)
        if ($token->needsRefresh()) {
            // Try to refresh the token automatically
            $this->attemptTokenRefresh($token);
            // Reload the token to get updated data
            $token->refresh();
        }

        return $token;
    }
}
