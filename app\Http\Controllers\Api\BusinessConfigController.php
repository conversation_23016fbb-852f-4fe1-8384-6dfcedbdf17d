<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BusinessConfig;
use App\Traits\ApiResponseTrait;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

final class BusinessConfigController extends Controller
{
    use ApiResponseTrait;

    /**
     * Get all business configurations
     */
    public function index(Request $request): JsonResponse
    {
        $configs = BusinessConfig::active()
            ->orderBy('category')
            ->orderBy('key')
            ->get()
            ->groupBy('category');

        return $this->successResponse([
            'configurations' => $configs,
        ]);
    }

    /**
     * Get configuration by key
     */
    public function getConfig(string $key): JsonResponse
    {
        $config = BusinessConfig::where('key', $key)
            ->where('is_active', true)
            ->first();

        if (!$config) {
            return $this->errorResponse('Configuration not found', 404);
        }

        return $this->successResponse([
            'key'         => $config->key,
            'category'    => $config->category,
            'value'       => $config->value,
            'description' => $config->description,
        ]);
    }

    /**
     * Update configuration by key
     */
    public function updateConfig(Request $request, string $key): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'value'       => 'required',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $config = BusinessConfig::where('key', $key)->first();

        if (!$config) {
            return $this->errorResponse('Configuration not found', 404);
        }

        $config->update([
            'value'       => $request->input('value'),
            'description' => $request->input('description', $config->description),
        ]);

        return $this->successResponse([
            'message' => 'Configuration updated successfully',
            'config'  => [
                'key'         => $config->key,
                'category'    => $config->category,
                'value'       => $config->value,
                'description' => $config->description,
            ],
        ]);
    }

    /**
     * Get configurations by category
     */
    public function getByCategory(string $category): JsonResponse
    {
        $configs = BusinessConfig::getByCategory($category);

        return $this->successResponse([
            'category' => $category,
            'configs'  => $configs,
        ]);
    }

    /**
     * Get available appointment slots
     */
    public function getAvailableSlots(Request $request): JsonResponse
    {
        $date = $request->input('date', Carbon::now()->format('Y-m-d'));

        try {
            $carbonDate = Carbon::createFromFormat('Y-m-d', $date);
        } catch (\Exception $e) {
            return $this->errorResponse('Invalid date format. Use Y-m-d format.', 400);
        }

        $slots = BusinessConfig::getAvailableSlots($carbonDate);

        return $this->successResponse([
            'date'           => $date,
            'formatted_date' => $carbonDate->format('l, M j, Y'),
            'is_weekend'     => $carbonDate->isWeekend(),
            'is_open'        => BusinessConfig::isBusinessOpen($carbonDate),
            'slots'          => $slots,
        ]);
    }

    /**
     * Get next available slots for multiple days
     */
    public function getNextAvailableSlots(Request $request): JsonResponse
    {
        $days = $request->input('days', 7);
        $days = min(max($days, 1), 30); // Limit between 1-30 days

        $slots = BusinessConfig::getNextAvailableSlots($days);

        return $this->successResponse([
            'days_requested'  => $days,
            'available_slots' => $slots,
        ]);
    }

    /**
     * Get time slots configuration
     */
    public function getTimeSlotsConfig(): JsonResponse
    {
        $config      = BusinessConfig::getTimeSlotsConfig();
        $timePeriods = BusinessConfig::getGlobalTimePeriods();

        return $this->successResponse([
            'time_slots_config'   => $config,
            'global_time_periods' => $timePeriods,
        ]);
    }

    /**
     * Update time slots configuration
     */
    public function updateTimeSlotsConfig(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'global_time_periods.morning'   => 'array|size:2',
            'global_time_periods.afternoon' => 'array|size:2',
            'global_time_periods.evening'   => 'array|size:2',
            'slot_duration_minutes'         => 'integer|min:15|max:480',
            'buffer_minutes'                => 'integer|min:0|max:60',
            'max_advance_days'              => 'integer|min:1|max:365',
            'min_advance_hours'             => 'integer|min:0|max:168',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $currentConfig = BusinessConfig::getTimeSlotsConfig();
        $updatedConfig = array_merge($currentConfig, $request->all());

        BusinessConfig::set('time_slots_config', $updatedConfig, 'scheduling');

        return $this->successResponse([
            'message' => 'Time slots configuration updated successfully',
            'config'  => $updatedConfig,
        ]);
    }

    /**
     * Get weekend settings
     */
    public function getWeekendSettings(): JsonResponse
    {
        $settings = BusinessConfig::getWeekendSettings();

        return $this->successResponse([
            'weekend_settings' => $settings,
        ]);
    }

    /**
     * Update weekend settings
     */
    public function updateWeekendSettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'saturday_enabled'           => 'boolean',
            'sunday_enabled'             => 'boolean',
            'weekend_hours.saturday'     => 'nullable|array|size:2',
            'weekend_hours.sunday'       => 'nullable|array|size:2',
            'weekend_pricing_multiplier' => 'numeric|min:1|max:3',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $currentSettings = BusinessConfig::getWeekendSettings();
        $updatedSettings = array_merge($currentSettings, $request->all());

        BusinessConfig::set('weekend_settings', $updatedSettings, 'scheduling');

        return $this->successResponse([
            'message'  => 'Weekend settings updated successfully',
            'settings' => $updatedSettings,
        ]);
    }

    /**
     * Get unavailable dates
     */
    public function getUnavailableDates(): JsonResponse
    {
        $unavailableDates = BusinessConfig::getUnavailableDates();

        return $this->successResponse([
            'unavailable_dates' => $unavailableDates,
        ]);
    }

    /**
     * Add unavailable date
     */
    public function addUnavailableDate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date_format:Y-m-d',
            'type' => 'required|in:holidays,maintenance_days,vacation_periods',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $date = $request->input('date');
        $type = $request->input('type');

        if ($type === 'vacation_periods') {
            $validator = Validator::make($request->all(), [
                'end_date' => 'required|date_format:Y-m-d|after_or_equal:date',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
            }

            $unavailableDates                       = BusinessConfig::getUnavailableDates();
            $unavailableDates['vacation_periods'][] = [
                'start' => $date,
                'end'   => $request->input('end_date'),
            ];

            BusinessConfig::set('unavailable_dates', $unavailableDates, 'scheduling');
        } else {
            $success = BusinessConfig::addUnavailableDate($date, $type);
            if (!$success) {
                return $this->errorResponse('Date already exists in unavailable dates', 409);
            }
        }

        return $this->successResponse([
            'message' => 'Unavailable date added successfully',
            'date'    => $date,
            'type'    => $type,
        ]);
    }

    /**
     * Remove unavailable date
     */
    public function removeUnavailableDate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date_format:Y-m-d',
            'type' => 'required|in:holidays,maintenance_days',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $date = $request->input('date');
        $type = $request->input('type');

        $success = BusinessConfig::removeUnavailableDate($date, $type);
        if (!$success) {
            return $this->errorResponse('Date not found in unavailable dates', 404);
        }

        return $this->successResponse([
            'message' => 'Unavailable date removed successfully',
            'date'    => $date,
            'type'    => $type,
        ]);
    }

    /**
     * Get disabled time slots
     */
    public function getDisabledTimeSlots(): JsonResponse
    {
        $disabledSlots = BusinessConfig::getDisabledTimeSlots();

        return $this->successResponse([
            'disabled_time_slots' => $disabledSlots,
        ]);
    }

    /**
     * Disable specific time slot for a date
     */
    public function disableTimeSlot(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date_format:Y-m-d',
            'time' => 'required|date_format:H:i',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $date = $request->input('date');
        $time = $request->input('time');

        $success = BusinessConfig::disableTimeSlot($date, $time);
        if (!$success) {
            return $this->errorResponse('Time slot already disabled for this date', 409);
        }

        return $this->successResponse([
            'message' => 'Time slot disabled successfully',
            'date'    => $date,
            'time'    => $time,
        ]);
    }

    /**
     * Enable specific time slot for a date
     */
    public function enableTimeSlot(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date_format:Y-m-d',
            'time' => 'required|date_format:H:i',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $date = $request->input('date');
        $time = $request->input('time');

        $success = BusinessConfig::enableTimeSlot($date, $time);
        if (!$success) {
            return $this->errorResponse('Time slot was not disabled for this date', 404);
        }

        return $this->successResponse([
            'message' => 'Time slot enabled successfully',
            'date'    => $date,
            'time'    => $time,
        ]);
    }

    /**
     * Get services
     */
    public function getServices(): JsonResponse
    {
        $services = BusinessConfig::getServices();

        return $this->successResponse([
            'services' => $services,
        ]);
    }

    /**
     * Get services by category
     */
    public function getServicesByCategory(string $category): JsonResponse
    {
        $services = BusinessConfig::getServicesByCategory($category);

        return $this->successResponse([
            'category' => $category,
            'services' => $services,
        ]);
    }

    /**
     * Update services
     */
    public function updateServices(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'services'               => 'required|array',
            'services.*.name'        => 'required|string|max:255',
            'services.*.duration'    => 'required|integer|min:15',
            'services.*.price'       => 'required|numeric|min:0',
            'services.*.description' => 'nullable|string|max:500',
            'services.*.category'    => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $services = $request->input('services');
        BusinessConfig::set('services', $services, 'business');

        return $this->successResponse([
            'message'  => 'Services updated successfully',
            'services' => $services,
        ]);
    }

    /**
     * Get business information
     */
    public function getBusinessInfo(): JsonResponse
    {
        $businessInfo = BusinessConfig::get('business_info', []);

        return $this->successResponse([
            'business_info' => $businessInfo,
        ]);
    }

    /**
     * Update business information
     */
    public function updateBusinessInfo(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name'                   => 'required|string|max:255',
            'type'                   => 'required|string|max:100',
            'description'            => 'nullable|string|max:500',
            'specialties'            => 'nullable|array',
            'contact.phone'          => 'nullable|string|max:20',
            'contact.email'          => 'nullable|email|max:255',
            'contact.address'        => 'nullable|string|max:500',
            'social_media.instagram' => 'nullable|string|max:100',
            'social_media.facebook'  => 'nullable|string|max:100',
            'social_media.website'   => 'nullable|url|max:255',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $businessInfo = $request->all();
        BusinessConfig::set('business_info', $businessInfo, 'business');

        return $this->successResponse([
            'message'       => 'Business information updated successfully',
            'business_info' => $businessInfo,
        ]);
    }

    /**
     * Get chatbot prompts
     */
    public function getChatbotPrompts(): JsonResponse
    {
        $prompts = BusinessConfig::getChatbotPrompts();

        return $this->successResponse([
            'chatbot_prompts' => $prompts,
        ]);
    }

    /**
     * Update chatbot prompts
     */
    public function updateChatbotPrompts(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'prompts'   => 'required|array',
            'prompts.*' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $prompts = $request->input('prompts');
        BusinessConfig::set('chatbot_prompts', $prompts, 'ai');

        return $this->successResponse([
            'message' => 'Chatbot prompts updated successfully',
            'prompts' => $prompts,
        ]);
    }

    /**
     * Get agent profile
     */
    public function getAgentProfile(): JsonResponse
    {
        $profile = BusinessConfig::getAgentProfile();

        return $this->successResponse([
            'agent_profile' => $profile,
            'shop_uid'      => BusinessConfig::getShopUID(),
            'agent_name'    => BusinessConfig::getAgentName(),
            'agent_type'    => BusinessConfig::getAgentType(),
            'shop_type'     => BusinessConfig::getShopType(),
        ]);
    }

    /**
     * Update agent profile
     */
    public function updateAgentProfile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'agent_type'    => 'required|string|max:100',
            'agent_name'    => 'required|string|max:100',
            'agent_gender'  => 'required|in:male,female,neutral',
            'personality'   => 'required|in:friendly,professional,casual,formal',
            'shop_type'     => 'required|string|max:100',
            'response_tone' => 'required|in:enthusiastic,helpful,professional,casual',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $currentProfile = BusinessConfig::getAgentProfile();
        $updatedProfile = array_merge($currentProfile, $request->only([
            'agent_type', 'agent_name', 'agent_gender', 'personality', 'shop_type', 'response_tone',
        ]));

        BusinessConfig::set('agent_profile', $updatedProfile, 'agent');

        return $this->successResponse([
            'message' => 'Agent profile updated successfully',
            'profile' => $updatedProfile,
        ]);
    }

    /**
     * Get integration channels
     */
    public function getIntegrationChannels(): JsonResponse
    {
        $channels        = BusinessConfig::getIntegrationChannels();
        $enabledChannels = BusinessConfig::getEnabledChannels();

        return $this->successResponse([
            'all_channels'     => $channels,
            'enabled_channels' => $enabledChannels,
        ]);
    }

    /**
     * Update integration channels
     */
    public function updateIntegrationChannels(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'instagram.enabled' => 'boolean',
            'facebook.enabled'  => 'boolean',
            'whatsapp.enabled'  => 'boolean',
            'twitter.enabled'   => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        $currentChannels = BusinessConfig::getIntegrationChannels();

        foreach ($request->all() as $channel => $settings) {
            if (isset($currentChannels[$channel])) {
                $currentChannels[$channel] = array_merge($currentChannels[$channel], $settings);
            }
        }

        BusinessConfig::set('integration_channels', $currentChannels, 'integrations');

        return $this->successResponse([
            'message'  => 'Integration channels updated successfully',
            'channels' => $currentChannels,
        ]);
    }
}