<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class KnowledgeChunk extends Model
{
    use HasFactory;

    protected $fillable = [
        'content',
        'embedding',
        'file_id',
        'chunk_index',
        'metadata',
    ];

    protected $casts = [
        'metadata'    => 'array',
        'file_id'     => 'integer',
        'chunk_index' => 'integer',
    ];

    public function getRouteKeyName(): string
    {
        return 'id';
    }

    /**
     * Get the knowledge file this chunk belongs to
     */
    public function knowledgeFile(): BelongsTo
    {
        return $this->belongsTo(KnowledgeFile::class, 'file_id');
    }

    /**
     * Set the embedding attribute
     */
    public function setEmbeddingAttribute(?array $value): void
    {
        if ($value !== null) {
            $this->attributes['embedding'] = '[' . implode(',', $value) . ']';
        }
    }

    /**
     * Get the embedding attribute
     */
    public function getEmbeddingAttribute(?string $value): ?array
    {
        if ($value === null) {
            return null;
        }

        // Remove brackets and convert to array
        $cleaned = trim($value, '[]');
        return array_map('floatval', explode(',', $cleaned));
    }

    /**
     * Scope to search by similarity using pgvector or fallback for other databases
     */
    public function scopeSimilarTo($query, array $embedding, int $limit = 5)
    {
        $embeddingString = '[' . implode(',', $embedding) . ']';
        $connection      = config('database.default');

        // Use pgvector if PostgreSQL with extension, otherwise fallback to simple search
        // Temporarily disabled pgvector until properly configured
        if (false && $connection === 'pgsql' && $this->hasPgVectorExtension()) {
            return $query->selectRaw('*, embedding <=> ? as distance', [$embeddingString])
                ->orderBy('distance', 'asc')
                ->limit($limit);
        }

        // Fallback: Simple content search without vector similarity
        return $query->select('*')
            ->selectRaw('0.5 as distance') // Default similarity score
            ->whereNotNull('embedding')
            ->orderBy('created_at', 'desc')
            ->limit($limit);
    }

    /**
     * Check if PostgreSQL has pgvector extension
     */
    private function hasPgVectorExtension(): bool
    {
        try {
            // Check if vector extension exists and if the embedding column is actually vector type
            $extensionExists = \DB::select("SELECT 1 FROM pg_extension WHERE extname = 'vector'");
            if (empty($extensionExists)) {
                return false;
            }

            // Also check if the embedding column is actually vector type
            $columnInfo = \DB::select("SELECT data_type FROM information_schema.columns WHERE table_name = 'knowledge_chunks' AND column_name = 'embedding'");
            return !empty($columnInfo) && ($columnInfo[0]->data_type === 'USER-DEFINED');
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Scope to get chunks for a specific file
     */
    public function scopeForFile($query, int $fileId)
    {
        return $query->where('file_id', $fileId)
            ->orderBy('chunk_index', 'asc');
    }
}