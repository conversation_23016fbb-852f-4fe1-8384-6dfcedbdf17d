<?php

declare (strict_types = 1);

namespace App\Services\Auth;

use App\Jobs\SendEmail;
use App\Models\AuthLog;
use App\Repositories\User\UserRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use PragmaRX\Google2FA\Google2FA;

class AuthService
{
    protected $repository;
    protected $google2fa;

    public function __construct(UserRepositoryInterface $repository)
    {
        $this->repository = $repository;
        $this->google2fa  = new Google2FA();
    }

    public function register(array $data)
    {
        $user  = $this->repository->create($data);
        $token = $user->createToken('auth_token')->plainTextToken;

        // Generate and send OTP
        $otp = $this->generateOtp($user->id);
        $this->sendWelcomeEmail($user, $otp);

        return [
            'user'  => $user,
            'token' => $token,
        ];
    }

    public function login(string $email, string $password, array $metadata = [])
    {
        $cacheKey = 'login_attempts:' . $email;
        $attempts = Cache::get($cacheKey, 0);

        if ($attempts >= 5) {
            $this->logAuthActivity('login', null, ['email' => $email], 'blocked');
            throw new \Exception('Account temporarily locked. Please try again later.', 423);
        }

        $user = $this->repository->findByEmail($email);

        if (!$user || !Hash::check($password, $user->password)) {
            Cache::put($cacheKey, $attempts + 1, now()->addMinutes(15));
            $this->logAuthActivity('login', $user?->id, ['email' => $email], 'failed');
            throw new \Exception('Invalid credentials');
        }

        Cache::forget($cacheKey);
        $this->logAuthActivity('login', $user->id);

        // Handle remember me functionality
        $tokenExpiration = isset($metadata['remember']) && $metadata['remember']
        ? now()->addMonths(6)
        : now()->addDay();

        // Generate token with device info and expiration
        $token = $user->createToken(
            request()->userAgent(),
            ['*'],
            $tokenExpiration
        )->plainTextToken;

        return [
            'user'  => $user,
            'token' => $token,
        ];
    }

    public function otpVerify(int $userId, string $otp)
    {
        $cachedOtp = cache()->get('email_verification_' . $userId);

        if (!$cachedOtp || $otp !== $cachedOtp) {
            throw new \Exception('Invalid or expired OTP');
        }

        $this->repository->verifyEmail($userId);
        cache()->forget('email_verification_' . $userId);

        return $this->repository->findById($userId);
    }

    public function forgotPassword(string $email)
    {
        $user = $this->repository->findByEmail($email);

        if (!$user) {
            throw new \Exception('User not found');
        }

        // Generate and send OTP
        $otp = $this->generateOtp($user->id);

        // Send password reset OTP email
        $this->sendPasswordResetOtpEmail($user, $otp);

        return true;
    }

    public function resetPassword(string $email, string $otp, string $password)
    {
        $user = $this->repository->findByEmail($email);

        if (!$user) {
            throw new \Exception('User not found');
        }

        $cachedOtp = cache()->get('password_reset_' . $user->id);

        if (!$cachedOtp || $otp !== $cachedOtp) {
            throw new \Exception('Invalid or expired OTP');
        }

        $this->repository->updatePassword($email, $password);
        cache()->forget('password_reset_' . $user->id);

        $this->sendPasswordChangedEmail($user);

        return true;
    }

    public function resendVerification(int $userId)
    {
        $user = $this->repository->findById($userId);

        if (!$user) {
            throw new \Exception('User not found');
        }

        if ($user->email_verified_at) {
            throw new \Exception('Email already verified');
        }

        // Generate new OTP
        $otp = $this->generateOtp($userId);

        // Send verification email with new OTP
        $this->sendVerificationEmail($user, $otp);

        return true;
    }

    protected function generateOtp(int $userId): string
    {
        // Generate a base32 encoded secret
        $secret = $this->google2fa->generateSecretKey();

        // Get the current OTP using the secret
        $otp = $this->google2fa->getCurrentOtp($secret);

        // Store in cache with different key for password reset
        $cacheKey = request()->is('*/forgot-password*')
        ? 'password_reset_' . $userId
        : 'email_verification_' . $userId;

        cache()->put($cacheKey, $otp, now()->addMinutes(10));

        return $otp;
    }

    protected function sendWelcomeEmail($user, $otp)
    {
        SendEmail::dispatch([
            'subject'    => 'Welcome to ' . config('app.name'),
            'to'         => $user->email,
            'email_body' => view('emails.auth.welcome', [
                'details' => [
                    'name' => $user->name,
                    'otp'  => $otp,
                ],
            ])->render(),
        ]);
    }

    protected function sendVerificationEmail($user, $otp)
    {
        SendEmail::dispatch([
            'subject'    => 'Email Verification OTP',
            'to'         => $user->email,
            'email_body' => view('emails.auth.verify-email', [
                'details' => [
                    'name' => $user->name,
                    'otp'  => $otp,
                ],
            ])->render(),
        ]);
    }

    protected function sendPasswordResetOtpEmail($user, $otp)
    {
        SendEmail::dispatch([
            'subject'    => 'Reset Password OTP',
            'to'         => $user->email,
            'email_body' => view('emails.auth.reset-password-otp', [
                'details' => [
                    'name' => $user->name,
                    'otp'  => $otp,
                ],
            ])->render(),
        ]);
    }

    protected function sendPasswordChangedEmail($user)
    {
        SendEmail::dispatch([
            'subject'    => 'Password Changed Successfully',
            'to'         => $user->email,
            'email_body' => view('emails.auth.password-changed', [
                'details' => [
                    'name' => $user->name,
                ],
            ])->render(),
        ]);
    }

    protected function logAuthActivity(string $action, $userId = null, array $metadata = [], string $status = 'success'): void
    {
        AuthLog::create([
            'user_id'    => $userId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'action'     => $action,
            'status'     => $status,
            'metadata'   => $metadata,
        ]);
    }

    protected function validatePasswordStrength(string $password): bool
    {
        $pattern = '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/';
        return (bool) preg_match($pattern, $password);
    }
}