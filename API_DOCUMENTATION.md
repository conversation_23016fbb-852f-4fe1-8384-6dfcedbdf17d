# Instagram Chatbot API Documentation

## Overview

This Laravel 12 Instagram Chatbot API provides comprehensive business configuration management with advanced scheduling features, multi-channel integration, and AI agent personality customization.

## Base URL

```
https://your-domain.com/api
```

## Authentication

All protected endpoints require Bearer token authentication using Laravel Sanctum.

```http
Authorization: Bearer {your-token}
```

---

## 🤖 Agent Profile Endpoints

### Get Agent Profile

```http
GET /api/v1/agent/profile
```

**Response:**

```json
{
    "status": "success",
    "data": {
        "agent_profile": {
            "agent_type": "Sales & Marketing",
            "agent_name": "<PERSON>",
            "agent_gender": "female",
            "personality": "friendly",
            "shop_uid": "FRAME-A1B2C3D4",
            "shop_type": "Car wrap & painting",
            "response_tone": "enthusiastic"
        },
        "shop_uid": "FRAME-A1B2C3D4",
        "agent_name": "<PERSON>",
        "agent_type": "Sales & Marketing",
        "shop_type": "Car wrap & painting"
    }
}
```

### Update Agent Profile

```http
PUT /api/v1/agent/profile
```

**Request Body:**

```json
{
    "agent_type": "Sales & Marketing",
    "agent_name": "<PERSON>",
    "agent_gender": "female",
    "personality": "friendly",
    "shop_type": "Car wrap & painting",
    "response_tone": "enthusiastic"
}
```

**Parameters:**

-   `agent_type`: string (required) - Type of agent (e.g., "Sales & Marketing", "Customer Service")
-   `agent_name`: string (required) - Agent's name
-   `agent_gender`: enum (required) - "male", "female", "neutral"
-   `personality`: enum (required) - "friendly", "professional", "casual", "formal"
-   `shop_type`: string (required) - Type of business
-   `response_tone`: enum (required) - "enthusiastic", "helpful", "professional", "casual"

---

## 🔗 Integration Channels Endpoints

### Get Integration Channels

```http
GET /api/v1/integrations/channels
```

**Response:**

```json
{
    "status": "success",
    "data": {
        "all_channels": {
            "instagram": {
                "enabled": true,
                "webhook_url": "/api/instagram/webhook",
                "priority": 1
            },
            "facebook": {
                "enabled": false,
                "webhook_url": "/api/facebook/webhook",
                "priority": 2
            },
            "whatsapp": {
                "enabled": false,
                "webhook_url": "/api/whatsapp/webhook",
                "priority": 3
            },
            "twitter": {
                "enabled": false,
                "webhook_url": "/api/twitter/webhook",
                "priority": 4
            }
        },
        "enabled_channels": {
            "instagram": {
                "enabled": true,
                "webhook_url": "/api/instagram/webhook",
                "priority": 1
            }
        }
    }
}
```

### Update Integration Channels

```http
PUT /api/v1/integrations/channels
```

**Request Body:**

```json
{
    "instagram": {
        "enabled": true
    },
    "facebook": {
        "enabled": false
    },
    "whatsapp": {
        "enabled": true
    },
    "twitter": {
        "enabled": false
    }
}
```

---

## 🏢 Business Configuration Endpoints

### Get Configuration by Key

```http
GET /api/v1/business/config/{key}
```

### Update Configuration by Key

```http
PUT /api/v1/business/config/{key}
```

**Request Body:**

```json
{
    "value": {
        /* configuration value */
    },
    "description": "Optional description"
}
```

### Get Configurations by Category

```http
GET /api/v1/business/config/category/{category}
```

**Categories:** `agent`, `integrations`, `scheduling`, `business`, `ai`

### Get Business Information

```http
GET /api/v1/business/info
```

### Update Business Information

```http
PUT /api/v1/business/info
```

**Request Body:**

```json
{
    "name": "Frame Auto Styling",
    "type": "automotive",
    "description": "Professional car wrap and painting services",
    "specialties": ["car wrapping", "custom paint jobs", "vehicle graphics"],
    "contact": {
        "phone": "******-FRAME-1",
        "email": "<EMAIL>",
        "address": "123 Auto Center Dr, Car City, State 12345"
    },
    "social_media": {
        "instagram": "@frameautostyling",
        "facebook": "FrameAutoStyling",
        "website": "https://frameautostyling.com"
    }
}
```

### Get Services

```http
GET /api/v1/business/services
```

### Update Services

```http
PUT /api/v1/business/services
```

**Request Body:**

```json
{
    "services": {
        "car_wrap_full": {
            "name": "Full Car Wrap",
            "duration": 480,
            "price": 2500.0,
            "description": "Complete vehicle wrap installation",
            "category": "wrapping"
        },
        "paint_job_full": {
            "name": "Full Paint Job",
            "duration": 720,
            "price": 3500.0,
            "description": "Complete vehicle paint job",
            "category": "painting"
        }
    }
}
```

### Get Services by Category

```http
GET /api/v1/business/services/category/{category}
```

---

## 📅 Enhanced Scheduling Endpoints

### Get Time Slots Configuration

```http
GET /api/v1/scheduling/time-slots/config
```

**Response:**

```json
{
    "status": "success",
    "data": {
        "time_slots_config": {
            "global_time_periods": {
                "morning": ["09:00", "12:00"],
                "afternoon": ["13:00", "17:00"],
                "evening": ["17:00", "20:00"]
            },
            "slot_duration_minutes": 60,
            "buffer_minutes": 15,
            "max_advance_days": 30,
            "min_advance_hours": 2,
            "slots_per_period": {
                "morning": 3,
                "afternoon": 4,
                "evening": 3
            }
        },
        "global_time_periods": {
            "morning": ["09:00", "12:00"],
            "afternoon": ["13:00", "17:00"],
            "evening": ["17:00", "20:00"]
        }
    }
}
```

### Update Time Slots Configuration

```http
PUT /api/v1/scheduling/time-slots/config
```

**Request Body:**

```json
{
    "global_time_periods": {
        "morning": ["09:00", "12:00"],
        "afternoon": ["13:00", "17:00"],
        "evening": ["17:00", "20:00"]
    },
    "slot_duration_minutes": 60,
    "buffer_minutes": 15,
    "max_advance_days": 30,
    "min_advance_hours": 2
}
```

### Get Available Slots

```http
GET /api/v1/scheduling/slots/available?date=2025-03-15
```

**Parameters:**

-   `date`: string (optional) - Date in Y-m-d format. Defaults to today.

**Response:**

```json
{
    "status": "success",
    "data": {
        "date": "2025-03-15",
        "formatted_date": "Saturday, Mar 15, 2025",
        "is_weekend": true,
        "is_open": true,
        "slots": [
            {
                "time": "09:00",
                "formatted": "9:00 AM",
                "datetime": "2025-03-15T09:00:00.000000Z",
                "period": "morning"
            },
            {
                "time": "10:15",
                "formatted": "10:15 AM",
                "datetime": "2025-03-15T10:15:00.000000Z",
                "period": "morning"
            }
        ]
    }
}
```

### Get Next Available Slots

```http
GET /api/v1/scheduling/slots/next-available?days=7
```

**Parameters:**

-   `days`: integer (optional) - Number of days to look ahead (1-30). Defaults to 7.

**Response:**

```json
{
    "status": "success",
    "data": {
        "days_requested": 7,
        "available_slots": {
            "2025-03-15": {
                "date": "2025-03-15",
                "formatted_date": "Saturday, Mar 15",
                "is_weekend": true,
                "pricing_multiplier": 1.2,
                "slots": [
                    {
                        "time": "09:00",
                        "formatted": "9:00 AM",
                        "datetime": "2025-03-15T09:00:00.000000Z",
                        "period": "morning"
                    }
                ]
            }
        }
    }
}
```

### Get Weekend Settings

```http
GET /api/v1/scheduling/weekend/settings
```

### Update Weekend Settings

```http
PUT /api/v1/scheduling/weekend/settings
```

**Request Body:**

```json
{
    "saturday_enabled": true,
    "sunday_enabled": false,
    "weekend_hours": {
        "saturday": ["09:00", "16:00"],
        "sunday": null
    },
    "weekend_pricing_multiplier": 1.2
}
```

### Get Unavailable Dates

```http
GET /api/v1/scheduling/unavailable-dates
```

**Response:**

```json
{
    "status": "success",
    "data": {
        "unavailable_dates": {
            "holidays": [
                "2025-12-25",
                "2025-01-01",
                "2025-07-04",
                "2025-11-28"
            ],
            "vacation_periods": [
                {
                    "start": "2025-08-15",
                    "end": "2025-08-22"
                }
            ],
            "maintenance_days": ["2025-06-15", "2025-09-10"]
        }
    }
}
```

### Add Unavailable Date

```http
POST /api/v1/scheduling/unavailable-dates
```

**Request Body:**

```json
{
    "date": "2025-12-25",
    "type": "holidays"
}
```

**For vacation periods:**

```json
{
    "date": "2025-08-15",
    "end_date": "2025-08-22",
    "type": "vacation_periods"
}
```

**Parameters:**

-   `date`: string (required) - Date in Y-m-d format
-   `type`: enum (required) - "holidays", "maintenance_days", "vacation_periods"
-   `end_date`: string (required for vacation_periods) - End date in Y-m-d format

### Remove Unavailable Date

```http
DELETE /api/v1/scheduling/unavailable-dates
```

**Request Body:**

```json
{
    "date": "2025-12-25",
    "type": "holidays"
}
```

### Get Disabled Time Slots

```http
GET /api/v1/scheduling/disabled-slots
```

**Response:**

```json
{
    "status": "success",
    "data": {
        "disabled_time_slots": {
            "2025-03-15": ["10:00", "14:00"],
            "2025-04-20": ["09:00", "11:00", "15:00"],
            "2025-05-10": ["13:00"]
        }
    }
}
```

### Disable Time Slot

```http
POST /api/v1/scheduling/disabled-slots
```

**Request Body:**

```json
{
    "date": "2025-03-15",
    "time": "10:00"
}
```

### Enable Time Slot

```http
DELETE /api/v1/scheduling/disabled-slots
```

**Request Body:**

```json
{
    "date": "2025-03-15",
    "time": "10:00"
}
```

---

## 🤖 AI Chatbot Endpoints

### Test Chatbot

```http
POST /api/v1/chatbot/test
```

**Request Body:**

```json
{
    "message": "I want to book an appointment for a car wrap"
}
```

### Get Chatbot Prompts

```http
GET /api/v1/chatbot/prompts
```

### Update Chatbot Prompts

```http
PUT /api/v1/chatbot/prompts
```

**Request Body:**

```json
{
    "prompts": {
        "greeting": "Hey there! 👋 I'm Kristen from Frame Auto Styling!",
        "services_inquiry": "We specialize in full car wraps, partial wraps, custom paint jobs, and design consultations.",
        "appointment_prompt": "Perfect! I'd love to get you scheduled.",
        "appointment_confirmation": "Awesome! I found some great time slots for you.",
        "business_hours_info": "We're open Monday-Friday 9 AM to 6 PM, and Saturday 9 AM to 4 PM.",
        "pricing_info": "Our pricing varies by project complexity. Full wraps start at $2,500.",
        "weekend_info": "Saturday appointments are available with a small weekend premium."
    }
}
```

---

## 📚 Knowledge Base Endpoints

### Upload Knowledge File

```http
POST /api/v1/knowledge/upload
```

**Request Body:** (multipart/form-data)

```
file: [PDF/Word/Text file]
```

### Get Knowledge Files

```http
GET /api/v1/knowledge/files
```

### Delete Knowledge File

```http
DELETE /api/v1/knowledge/files/{id}
```

---

## 🔑 Meta Token Endpoints

### Get Meta Tokens

```http
GET /api/v1/meta/tokens
```

### Create Meta Token

```http
POST /api/v1/meta/tokens
```

### Update Meta Token

```http
PUT /api/v1/meta/tokens/{id}
```

### Delete Meta Token

```http
DELETE /api/v1/meta/tokens/{id}
```

---

## 🚀 Instagram Webhook Endpoints

### Webhook Handler (Public)

```http
POST /api/instagram/webhook
```

### Webhook Verification (Public)

```http
GET /api/instagram/webhook
```

---

## 📊 Health Check

### Health Status

```http
GET /api/health
```

**Response:**

```json
{
    "status": "ok",
    "timestamp": "2025-01-15T10:30:00.000000Z",
    "service": "Instagram Chatbot API"
}
```

---

## 🎯 Key Features

### ✅ **Agent Profile Management**

-   Customizable AI agent personality and type
-   Unique shop UID generation
-   Gender and response tone configuration

### ✅ **Multi-Channel Integration**

-   Instagram, Facebook, WhatsApp, Twitter support
-   Priority-based channel management
-   Enable/disable channels dynamically

### ✅ **Advanced Scheduling**

-   Global time periods (morning, afternoon, evening)
-   Weekend-specific settings with pricing multipliers
-   Unavailable dates (holidays, vacations, maintenance)
-   Specific date/time slot disabling
-   Configurable slot duration and buffer times

### ✅ **Business Configuration**

-   Complete business information management
-   Service categories and pricing
-   Flexible JSON-based configuration storage

### ✅ **AI Chatbot Customization**

-   Industry-specific conversation prompts
-   Personality-driven responses
-   Appointment booking integration

### ✅ **Knowledge Base**

-   PDF, Word, and text file upload
-   Vector-based similarity search
-   Automatic text extraction and chunking

---

## 📋 Error Responses

All endpoints return standardized error responses:

```json
{
    "status": "error",
    "message": "Error description",
    "errors": {
        "field_name": ["Validation error message"]
    }
}
```

**Common HTTP Status Codes:**

-   `200` - Success
-   `201` - Created
-   `400` - Bad Request
-   `401` - Unauthorized
-   `404` - Not Found
-   `409` - Conflict
-   `422` - Validation Error
-   `500` - Internal Server Error

---

## 🔧 Configuration Examples

### Car Wrap & Paint Shop

```json
{
    "agent_type": "Sales & Marketing",
    "shop_type": "Car wrap & painting",
    "services": {
        "car_wrap_full": {
            "name": "Full Car Wrap",
            "duration": 480,
            "price": 2500.0,
            "category": "wrapping"
        }
    }
}
```

### Beauty Salon

```json
{
    "agent_type": "Beauty Consultant",
    "shop_type": "Beauty & Wellness",
    "services": {
        "haircut": {
            "name": "Haircut & Style",
            "duration": 45,
            "price": 65.0,
            "category": "hair"
        }
    }
}
```

### Healthcare Clinic

```json
{
    "agent_type": "Medical Assistant",
    "shop_type": "Healthcare",
    "services": {
        "checkup": {
            "name": "General Checkup",
            "duration": 30,
            "price": 120.0,
            "category": "medical"
        }
    }
}
```

The API is designed to be **100% business-agnostic** and can be customized for any service-based business with just configuration changes!
