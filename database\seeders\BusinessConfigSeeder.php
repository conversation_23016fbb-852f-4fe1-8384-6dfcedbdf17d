<?php

declare (strict_types = 1);

namespace Database\Seeders;

use App\Models\BusinessConfig;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

final class BusinessConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedAgentProfile();
        $this->seedIntegrationChannels();
        $this->seedBusinessHours();
        $this->seedTimeSlotsConfig();
        $this->seedWeekendSettings();
        $this->seedUnavailableDates();
        $this->seedServices();
        $this->seedBusinessInfo();
        $this->seedChatbotPrompts();
    }

    private function seedAgentProfile(): void
    {
        BusinessConfig::set(
            'agent_profile',
            [
                'agent_type'    => 'Vehicle Customization Specialist',
                'agent_name'    => 'Sarah',
                'agent_gender'  => 'female',
                'personality'   => 'professional',
                'shop_uid'      => 'WRAP-' . strtoupper(substr(md5(uniqid()), 0, 8)),
                'shop_type'     => 'Auto Styling & Protection',
                'response_tone' => 'enthusiastic',
            ],
            'agent',
            'AI agent profile configuration for auto styling business'
        );
    }

    private function seedIntegrationChannels(): void
    {
        BusinessConfig::set(
            'integration_channels',
            [
                'instagram' => [
                    'enabled'     => true,
                    'webhook_url' => '/api/instagram/webhook',
                    'priority'    => 1,
                    'features'    => ['messaging', 'story_mentions', 'comments'],
                ],
                'facebook'  => [
                    'enabled'     => true,
                    'webhook_url' => '/api/facebook/webhook',
                    'priority'    => 2,
                    'features'    => ['messaging', 'posts', 'reviews'],
                ],
                'whatsapp'  => [
                    'enabled'     => false,
                    'webhook_url' => '/api/whatsapp/webhook',
                    'priority'    => 3,
                ],
            ],
            'integrations',
            'Social media integration channels configuration'
        );
    }

    private function seedBusinessHours(): void
    {
        BusinessConfig::set(
            'business_hours',
            [
                'monday'    => ['09:00', '18:00'],
                'tuesday'   => ['09:00', '18:00'],
                'wednesday' => ['09:00', '18:00'],
                'thursday'  => ['09:00', '18:00'],
                'friday'    => ['09:00', '18:00'],
                'saturday'  => ['10:00', '16:00'],
                'sunday'    => null, // Closed
            ],
            'scheduling',
            'Regular business operating hours'
        );
    }

    private function seedTimeSlotsConfig(): void
    {
        BusinessConfig::set(
            'time_slots_config',
            [
                'global_time_periods'   => [
                    'morning'   => ['09:00', '12:00'],
                    'afternoon' => ['13:00', '16:00'],
                    'evening'   => ['16:00', '18:00'],
                ],
                'slot_duration_minutes' => 120, // 2-hour slots for detailed work
                'buffer_minutes'        => 30, // Buffer between appointments
                'max_advance_days'      => 60, // Book up to 2 months ahead
                'min_advance_hours'     => 24, // At least 24 hours notice
                'slots_per_period'      => [
                    'morning'   => 2,
                    'afternoon' => 2,
                    'evening'   => 1,
                ],
            ],
            'scheduling',
            'Appointment time slot configuration'
        );
    }

    private function seedWeekendSettings(): void
    {
        BusinessConfig::set(
            'weekend_settings',
            [
                'saturday_enabled'           => true,
                'sunday_enabled'             => false,
                'weekend_hours'              => [
                    'saturday' => ['10:00', '16:00'],
                    'sunday'   => null,
                ],
                'weekend_pricing_multiplier' => 1.2, // 20% weekend premium
            ],
            'scheduling',
            'Weekend availability and pricing settings'
        );
    }

    private function seedUnavailableDates(): void
    {
        $currentYear = Carbon::now()->year;

        BusinessConfig::set(
            'unavailable_dates',
            [
                'holidays'         => [
                    "$currentYear-01-01", // New Year's Day
                    "$currentYear-12-25", // Christmas
                    "$currentYear-12-26", // Boxing Day
                    "$currentYear-07-04", // Independence Day
                    "$currentYear-11-23", // Thanksgiving
                ],
                'maintenance_days' => [
                    Carbon::now()->addMonths(2)->format('Y-m-d'), // Equipment maintenance
                    Carbon::now()->addMonths(4)->format('Y-m-d'), // Staff training
                ],
                'vacation_periods' => [
                    [
                        'start' => Carbon::now()->addMonths(6)->startOfWeek()->format('Y-m-d'),
                        'end'   => Carbon::now()->addMonths(6)->endOfWeek()->format('Y-m-d'),
                    ],
                ],
            ],
            'scheduling',
            'Holidays and unavailable dates'
        );
    }

    private function seedServices(): void
    {
        BusinessConfig::set(
            'services',
            [
                'full_wrap'           => [
                    'name'        => 'Premium Full Vehicle Wrap',
                    'duration'    => 480, // 8 hours
                    'price'       => 2800.00,
                    'description' => 'Complete vehicle wrap with premium vinyl and professional installation',
                    'category'    => 'wrapping',
                    'materials'   => ['3M', 'Avery Dennison'],
                    'warranty'    => '5 years',
                ],
                'partial_wrap'        => [
                    'name'        => 'Custom Partial Wrap',
                    'duration'    => 240, // 4 hours
                    'price'       => 1400.00,
                    'description' => 'Partial vehicle wrap for specific sections (hood, roof, sides)',
                    'category'    => 'wrapping',
                    'materials'   => ['3M', 'Avery Dennison'],
                    'warranty'    => '5 years',
                ],
                'paint_protection'    => [
                    'name'        => 'Paint Protection Film',
                    'duration'    => 360, // 6 hours
                    'price'       => 1800.00,
                    'description' => 'Premium PPF installation with self-healing properties',
                    'category'    => 'protection',
                    'materials'   => ['XPEL', 'SunTek'],
                    'warranty'    => '10 years',
                ],
                'ceramic_coating'     => [
                    'name'        => 'Ceramic Coating',
                    'duration'    => 480, // 8 hours
                    'price'       => 1500.00,
                    'description' => 'Professional ceramic coating application',
                    'category'    => 'protection',
                    'materials'   => ['Ceramic Pro', 'IGL Coatings'],
                    'warranty'    => '5 years',
                ],
                'commercial_graphics' => [
                    'name'        => 'Commercial Fleet Graphics',
                    'duration'    => 360, // 6 hours
                    'price'       => 2000.00,
                    'description' => 'Professional fleet graphics with business branding',
                    'category'    => 'commercial',
                    'materials'   => ['3M', 'Avery Dennison'],
                    'warranty'    => '3 years',
                ],
                'design_consultation' => [
                    'name'        => 'Design Consultation',
                    'duration'    => 60, // 1 hour
                    'price'       => 100.00,
                    'description' => 'Professional design consultation and mockup creation',
                    'category'    => 'consultation',
                    'materials'   => ['Digital Mockup', 'Sample Materials'],
                ],
            ],
            'business',
            'Available services with pricing and details'
        );
    }

    private function seedBusinessInfo(): void
    {
        BusinessConfig::set(
            'business_info',
            [
                'name'           => 'Elite Auto Styling',
                'type'           => 'automotive',
                'description'    => 'Premium vehicle wrapping, paint protection, and styling services',
                'specialties'    => [
                    'Vehicle Wraps',
                    'Paint Protection Film',
                    'Ceramic Coating',
                    'Commercial Fleet Graphics',
                    'Custom Designs',
                ],
                'contact'        => [
                    'phone'   => '******-AUTO-WRAP',
                    'email'   => '<EMAIL>',
                    'address' => '123 Custom Drive, Automotive District, CA 90210',
                ],
                'social_media'   => [
                    'instagram' => '@eliteautostyling',
                    'facebook'  => 'EliteAutoStyling',
                    'website'   => 'https://eliteautostyling.com',
                ],
                'certifications' => [
                    '3M Preferred Installer',
                    'XPEL Certified',
                    'Ceramic Pro Certified',
                ],
            ],
            'business',
            'Business information and contact details'
        );
    }

    private function seedChatbotPrompts(): void
    {
        BusinessConfig::set(
            'chatbot_prompts',
            [
                'greeting'           => "Hi there! 👋 I'm Sarah from Elite Auto Styling. Ready to transform your vehicle with our premium wraps and protection services? How can I assist you today?",

                'services_inquiry'   => "We offer a range of premium services including:\n" .
                "🚗 Full & Partial Vehicle Wraps\n" .
                "🛡️ Paint Protection Film\n" .
                "✨ Ceramic Coating\n" .
                "🚐 Commercial Fleet Graphics\n" .
                "Which service interests you?",

                'appointment_prompt' => "I'd be happy to schedule your appointment! We offer flexible scheduling with 2-hour time slots. When would you prefer to bring your vehicle in?",

                'pricing_info'       => "Our pricing varies based on vehicle size and service complexity. Here's a general guide:\n" .
                "• Full Wraps: Starting at $2,800\n" .
                "• Partial Wraps: From $1,400\n" .
                "• Paint Protection: From $1,800\n" .
                "Would you like a detailed quote for your specific vehicle?",

                'warranty_info'      => "We stand behind our work with comprehensive warranties:\n" .
                "• Vehicle Wraps: 5-year warranty\n" .
                "• Paint Protection Film: 10-year warranty\n" .
                "• Ceramic Coating: 5-year warranty\n" .
                "All backed by our satisfaction guarantee!",

                'care_instructions'  => "To maintain your vehicle's new finish:\n" .
                "• Hand wash only\n" .
                "• Avoid automatic car washes\n" .
                "• Use pH-neutral products\n" .
                "Would you like our detailed care guide?",

                'business_hours'     => "We're open Monday-Friday 9 AM to 6 PM, and Saturday 10 AM to 4 PM. Closed on Sundays. Weekend appointments include a small premium but often have better availability!",

                'consultation_offer' => "Not sure which service is right for you? We offer professional design consultations for $100, which is credited toward your service when you book with us. Would you like to schedule a consultation?",

                'follow_up'          => "Haven't heard back from you - just checking if you have any questions about our services or would like to schedule an appointment? I'm here to help!",

                'testimonial_prompt' => "We love hearing from satisfied clients! If you're happy with our service, would you mind sharing your experience on our Instagram or Facebook page? We appreciate your feedback!",
            ],
            'ai',
            'Chatbot conversation prompts and responses'
        );
    }
}
