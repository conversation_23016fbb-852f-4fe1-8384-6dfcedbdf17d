<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meta_tokens', function (Blueprint $table) {
            $table->id();

            // Platform identification
            $table->string('platform')->default('facebook')->comment('Platform: facebook or instagram')->index();

            // Instagram fields (exact fields from Instagram API)
            $table->string('app_scope_id')->index()->comment('The app user\'s app-scoped ID (id field from API response)');
            $table->string('user_id')->nullable()->comment('The Instagram professional account ID (IG_ID) for webhook notifications')->index();
            $table->string('username')->nullable()->comment('The app user\'s Instagram username');
            $table->string('name')->nullable()->comment('The app user\'s name');
            $table->string('account_type')->nullable()->comment('The app user\'s account type (BUSINESS or MEDIA_CREATOR)');
            $table->text('profile_picture_url')->nullable()->comment('The URL for the app user\'s profile picture');
            $table->integer('followers_count')->nullable()->comment('The number of followers');
            $table->integer('follows_count')->nullable()->comment('The number of Instagram accounts the user follows');
            $table->integer('media_count')->nullable()->comment('The number of Media objects on the User');

            // Token information
            $table->text('access_token')->comment('The access token value from Meta/Instagram');
            $table->string('token_type')->default('bearer')->comment('Token type (usually bearer)');
            $table->timestamp('expires_at')->nullable()->comment('When the token expires')->index();
            $table->enum('status', ['active', 'expired', 'revoked'])->default('active')->index();
            $table->boolean('is_active')->default(true)->comment('Whether this token is currently active');

            // Permissions
            $table->json('scopes')->nullable()->comment('Array of granted permissions');
            $table->boolean('has_business_permission')->default(false)->comment('Whether the token has business permissions');

            // Facebook integration field (only if needed)
            $table->string('page_id')->index()->nullable()->comment('Facebook Page ID if connected to a Facebook page');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meta_tokens');
    }
};