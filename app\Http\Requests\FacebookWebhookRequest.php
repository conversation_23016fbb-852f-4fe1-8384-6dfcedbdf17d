<?php

declare (strict_types = 1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class FacebookWebhookRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'object'                               => 'required|string',
            'entry'                                => 'required|array',
            'entry.*.id'                           => 'required|string',
            'entry.*.time'                         => 'required|integer',
            'entry.*.messaging'                    => 'sometimes|array',
            'entry.*.messaging.*.sender'           => 'sometimes|array',
            'entry.*.messaging.*.sender.id'        => 'required_with:entry.*.messaging.*.sender|string',
            'entry.*.messaging.*.recipient'        => 'sometimes|array',
            'entry.*.messaging.*.recipient.id'     => 'required_with:entry.*.messaging.*.recipient|string',
            'entry.*.messaging.*.timestamp'        => 'sometimes|integer',
            'entry.*.messaging.*.message'          => 'sometimes|array',
            'entry.*.messaging.*.message.mid'      => 'sometimes|string',
            'entry.*.messaging.*.message.text'     => 'sometimes|string',
            'entry.*.messaging.*.postback'         => 'sometimes|array',
            'entry.*.messaging.*.postback.payload' => 'sometimes|string',
        ];
    }

    public function isMessage(): bool
    {
        $entries = $this->input('entry', []);

        foreach ($entries as $entry) {
            $messaging = $entry['messaging'] ?? [];
            foreach ($messaging as $message) {
                if (isset($message['message'])) {
                    return true;
                }
            }
        }

        return false;
    }

    public function isPostback(): bool
    {
        $entries = $this->input('entry', []);

        foreach ($entries as $entry) {
            $messaging = $entry['messaging'] ?? [];
            foreach ($messaging as $message) {
                if (isset($message['postback'])) {
                    return true;
                }
            }
        }

        return false;
    }

    public function getMessageData(): array
    {
        $entries = $this->input('entry', []);

        foreach ($entries as $entry) {
            $messaging = $entry['messaging'] ?? [];
            foreach ($messaging as $message) {
                if (isset($message['message'])) {
                    return [
                        'sender_id'       => $message['sender']['id'] ?? '',
                        'recipient_id'    => $message['recipient']['id'] ?? '',
                        'conversation_id' => 'fb_' . ($message['sender']['id'] ?? ''),
                        'content'         => $message['message']['text'] ?? '',
                        'timestamp'       => $message['timestamp'] ?? time(),
                        'message_id'      => $message['message']['mid'] ?? '',
                        'meta_data'       => [
                            'platform'     => 'facebook',
                            'entry_id'     => $entry['id'] ?? '',
                            'entry_time'   => $entry['time'] ?? time(),
                            'message_type' => 'text',
                            'raw_message'  => $message,
                        ],
                    ];
                }
            }
        }

        return [];
    }

    public function getPostbackData(): array
    {
        $entries = $this->input('entry', []);

        foreach ($entries as $entry) {
            $messaging = $entry['messaging'] ?? [];
            foreach ($messaging as $message) {
                if (isset($message['postback'])) {
                    return [
                        'sender_id'    => $message['sender']['id'] ?? '',
                        'recipient_id' => $message['recipient']['id'] ?? '',
                        'payload'      => $message['postback']['payload'] ?? '',
                        'timestamp'    => $message['timestamp'] ?? time(),
                        'meta_data'    => [
                            'platform'     => 'facebook',
                            'entry_id'     => $entry['id'] ?? '',
                            'entry_time'   => $entry['time'] ?? time(),
                            'message_type' => 'postback',
                            'raw_message'  => $message,
                        ],
                    ];
                }
            }
        }

        return [];
    }
}
