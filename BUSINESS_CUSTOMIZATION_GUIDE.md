# Business Configuration Customization Guide

The Laravel Instagram Chatbot system is now designed to work with **any type of business**. The database schema is completely flexible, and you only need to customize the default configuration data to match your specific business needs.

## 🏗️ **Universal Database Schema**

The `business_configs` table structure works for ALL business types:

```php
- id (unique identifier)
- key (configuration name)
- category (grouping: scheduling, business, ai)
- value (JSON - completely flexible)
- description (human-readable explanation)
- is_active (enable/disable configurations)
- timestamps (created_at, updated_at)
```

## 🎯 **Customization by Business Type**

### **1. Professional Services (Lawyers, Consultants, Accountants)**

```php
'services' => [
    'consultation' => [
        'name' => 'Initial Consultation',
        'duration' => 60,
        'price' => 150.00,
        'description' => 'One-hour consultation to discuss your needs'
    ],
    'document_review' => [
        'name' => 'Document Review',
        'duration' => 30,
        'price' => 75.00,
        'description' => 'Professional document review and analysis'
    ],
    'full_service' => [
        'name' => 'Comprehensive Service',
        'duration' => 180,
        'price' => 400.00,
        'description' => 'Complete service package'
    ]
]

'business_hours' => [
    'monday' => ['08:00', '18:00'],
    'tuesday' => ['08:00', '18:00'],
    'wednesday' => ['08:00', '18:00'],
    'thursday' => ['08:00', '18:00'],
    'friday' => ['08:00', '17:00'],
    'saturday' => null, // Closed
    'sunday' => null    // Closed
]

'chatbot_prompts' => [
    'greeting' => 'Welcome to [Firm Name]. How can we assist you with your legal/consulting needs today?',
    'services_inquiry' => 'We provide consultation, document review, and comprehensive legal services. Which service interests you?'
]
```

### **2. Healthcare/Medical (Doctors, Dentists, Therapists)**

```php
'services' => [
    'checkup' => [
        'name' => 'General Checkup',
        'duration' => 30,
        'price' => 120.00,
        'description' => 'Routine health examination'
    ],
    'consultation' => [
        'name' => 'Specialist Consultation',
        'duration' => 45,
        'price' => 200.00,
        'description' => 'Specialized medical consultation'
    ],
    'treatment' => [
        'name' => 'Treatment Session',
        'duration' => 60,
        'price' => 150.00,
        'description' => 'Therapeutic treatment session'
    ]
]

'appointment_slots' => [
    'duration_minutes' => 30,  // Shorter slots for medical
    'buffer_minutes' => 10,    // Quick turnaround
    'max_advance_days' => 60,  // Longer advance booking
    'min_advance_hours' => 24  // 24-hour notice
]

'chatbot_prompts' => [
    'greeting' => 'Hello! Welcome to [Clinic Name]. How can we help with your healthcare needs?',
    'appointment_prompt' => 'I can help you schedule an appointment with our medical team. What type of service do you need?'
]
```

### **3. Beauty/Wellness (Salons, Spas, Fitness)**

```php
'services' => [
    'haircut' => [
        'name' => 'Haircut & Style',
        'duration' => 45,
        'price' => 65.00,
        'description' => 'Professional haircut and styling'
    ],
    'massage' => [
        'name' => 'Relaxation Massage',
        'duration' => 90,
        'price' => 120.00,
        'description' => '90-minute full body massage'
    ],
    'facial' => [
        'name' => 'Facial Treatment',
        'duration' => 60,
        'price' => 85.00,
        'description' => 'Deep cleansing facial treatment'
    ]
]

'business_hours' => [
    'monday' => ['09:00', '20:00'],    // Extended hours
    'tuesday' => ['09:00', '20:00'],
    'wednesday' => ['09:00', '20:00'],
    'thursday' => ['09:00', '20:00'],
    'friday' => ['09:00', '21:00'],    // Late Friday
    'saturday' => ['08:00', '18:00'],  // Weekend hours
    'sunday' => ['10:00', '17:00']     // Sunday hours
]
```

### **4. Home Services (Cleaning, Repair, Maintenance)**

```php
'services' => [
    'house_cleaning' => [
        'name' => 'House Cleaning',
        'duration' => 120,
        'price' => 80.00,
        'description' => 'Complete house cleaning service'
    ],
    'deep_cleaning' => [
        'name' => 'Deep Cleaning',
        'duration' => 240,
        'price' => 150.00,
        'description' => 'Thorough deep cleaning service'
    ],
    'maintenance' => [
        'name' => 'Home Maintenance',
        'duration' => 180,
        'price' => 120.00,
        'description' => 'General home repair and maintenance'
    ]
]

'appointment_slots' => [
    'duration_minutes' => 120,  // Longer service windows
    'buffer_minutes' => 30,     // Travel time between locations
    'max_advance_days' => 14,   // Shorter booking window
    'min_advance_hours' => 4    // Same-day booking possible
]
```

### **5. Education/Training (Tutoring, Music Lessons, Coaching)**

```php
'services' => [
    'individual_lesson' => [
        'name' => 'Individual Lesson',
        'duration' => 60,
        'price' => 50.00,
        'description' => 'One-on-one tutoring session'
    ],
    'group_lesson' => [
        'name' => 'Group Lesson',
        'duration' => 90,
        'price' => 30.00,
        'description' => 'Small group learning session'
    ],
    'assessment' => [
        'name' => 'Skills Assessment',
        'duration' => 45,
        'price' => 75.00,
        'description' => 'Comprehensive skills evaluation'
    ]
]

'business_hours' => [
    'monday' => ['15:00', '21:00'],    // After school hours
    'tuesday' => ['15:00', '21:00'],
    'wednesday' => ['15:00', '21:00'],
    'thursday' => ['15:00', '21:00'],
    'friday' => ['15:00', '19:00'],
    'saturday' => ['09:00', '17:00'],  // Weekend availability
    'sunday' => ['10:00', '16:00']
]
```

### **6. Retail/E-commerce (Personal Shopping, Consultations)**

```php
'services' => [
    'personal_shopping' => [
        'name' => 'Personal Shopping Session',
        'duration' => 120,
        'price' => 100.00,
        'description' => 'Personalized shopping experience'
    ],
    'style_consultation' => [
        'name' => 'Style Consultation',
        'duration' => 60,
        'price' => 75.00,
        'description' => 'Fashion and style advice session'
    ],
    'wardrobe_planning' => [
        'name' => 'Wardrobe Planning',
        'duration' => 180,
        'price' => 200.00,
        'description' => 'Complete wardrobe makeover planning'
    ]
]
```

## 🔧 **How to Customize**

### **Step 1: Update Migration Before Running**

Edit the migration file **before** running `php artisan migrate`:

```bash
# Edit the file
nano database/migrations/2025_07_04_101429_create_business_configs_table.php

# Then run migration
php artisan migrate
```

### **Step 2: Update Existing Database**

If you've already migrated, update via the API or directly in the database:

```php
// Via BusinessConfig model
BusinessConfig::updateConfig('services', [
    'your_service' => [
        'name' => 'Your Service Name',
        'duration' => 60,
        'price' => 100.00,
        'description' => 'Service description'
    ]
]);

// Via API endpoint
PUT /api/v1/business/config/services
{
    "value": {
        "your_service": {
            "name": "Your Service Name",
            "duration": 60,
            "price": 100.00,
            "description": "Service description"
        }
    }
}
```

### **Step 3: Customize Chatbot Prompts**

Update the AI conversation style to match your business:

```php
'chatbot_prompts' => [
    'greeting' => 'Welcome to [Your Business]! How can I help you today?',
    'services_inquiry' => 'We offer [list your services]. What interests you?',
    'appointment_prompt' => 'I can schedule an appointment for you. When would you prefer?',
    // Add industry-specific prompts
    'pricing_info' => 'Our services start at $X. Would you like detailed pricing?',
    'location_info' => 'We\'re located at [address]. Would you like directions?'
]
```

## 🎯 **Key Benefits of This Approach**

✅ **Universal Schema**: Works for ANY business type  
✅ **Easy Customization**: Just update JSON values  
✅ **No Code Changes**: Modify through API or database  
✅ **Flexible Services**: Add/remove services as needed  
✅ **Scalable**: Supports complex business models  
✅ **Multi-location**: Can handle multiple business locations

## 📝 **Quick Setup Checklist**

1. ✅ **Business Info**: Update name, type, contact details
2. ✅ **Services**: Define your service offerings and pricing
3. ✅ **Hours**: Set your operating schedule
4. ✅ **Appointments**: Configure slot duration and booking rules
5. ✅ **Holidays**: Add your business closure dates
6. ✅ **Chatbot**: Customize conversation prompts for your industry
7. ✅ **Test**: Verify appointment booking works for your services

The system is now **100% business-agnostic** and can be adapted to any industry with just configuration changes!
