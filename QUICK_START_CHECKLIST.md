# Quick Start Checklist - Instagram API Setup

## ✅ Pre-Setup Checklist

- [ ] Laravel app running on `http://kortana-ai.test`
- [ ] Facebook Developer Account created
- [ ] Instagram Business Account ready
- [ ] Facebook Page connected to Instagram Business Account
- [ ] Postman collection imported with base_url updated

## ✅ Facebook/Meta Developer Setup (15 mins)

### Step 1: Create Facebook App
- [ ] Go to [developers.facebook.com](https://developers.facebook.com/)
- [ ] Create new "Business" app named "Kortana AI Chatbot"
- [ ] Note down **App ID** and **App Secret**

### Step 2: Add Products
- [ ] Add "Instagram Basic Display" product
- [ ] Add "Messenger" product
- [ ] Add your Instagram account as test user

### Step 3: Get Access Token
- [ ] Go to [Graph API Explorer](https://developers.facebook.com/tools/explorer/)
- [ ] Generate access token with permissions:
  - `pages_show_list`
  - `pages_read_engagement` 
  - `pages_manage_metadata`
  - `instagram_basic`
  - `instagram_manage_messages`
  - `instagram_manage_comments`
- [ ] Exchange for long-lived token using curl command
- [ ] Get Instagram Business Account ID

## ✅ Laravel Configuration (5 mins)

### Step 4: Environment Setup
Add to `.env` file:
```env
META_APP_ID=your_app_id_here
META_APP_SECRET=your_app_secret_here
META_WEBHOOK_VERIFY_TOKEN=my_custom_verify_token_123
META_PAGE_ACCESS_TOKEN=your_long_lived_token_here
INSTAGRAM_BUSINESS_ACCOUNT_ID=your_instagram_business_id
INSTAGRAM_PAGE_ID=your_facebook_page_id
```

- [ ] Update `.env` with your actual values
- [ ] Run `php artisan config:clear`

## ✅ Webhook Setup (10 mins)

### Step 5: Configure Webhooks
- [ ] In Facebook App Dashboard → Messenger → Settings
- [ ] Add Callback URL: `http://kortana-ai.test/api/webhooks/instagram`
- [ ] Set Verify Token: `my_custom_verify_token_123`
- [ ] Subscribe to: `messages`, `messaging_postbacks`, `message_deliveries`

### Step 6: Test Webhook Verification
```bash
curl -X GET "http://kortana-ai.test/api/webhooks/instagram?hub.mode=subscribe&hub.challenge=test123&hub.verify_token=my_custom_verify_token_123"
```
- [ ] Should return: `test123`

## ✅ Database Setup (5 mins)

### Step 7: Create Records via Postman

**Create Meta Token:**
```
POST http://kortana-ai.test/api/v1/meta-token
Authorization: Bearer YOUR_AUTH_TOKEN
```
```json
{
  "access_token": "your_long_lived_token",
  "page_id": "your_facebook_page_id",
  "app_id": "your_app_id",
  "app_secret": "your_app_secret",
  "webhook_verify_token": "my_custom_verify_token_123"
}
```

**Create Integration:**
```
POST http://kortana-ai.test/api/v1/integrations
Authorization: Bearer YOUR_AUTH_TOKEN
```
```json
{
  "platform": "instagram",
  "access_token": "your_long_lived_token",
  "page_id": "your_instagram_business_id",
  "webhook_url": "http://kortana-ai.test/api/webhooks/instagram",
  "is_active": true
}
```

- [ ] Meta token created successfully
- [ ] Integration record created successfully

## ✅ Testing (10 mins)

### Step 8: API Testing via Postman

**Test Message Sending:**
```
POST http://kortana-ai.test/api/v1/social/instagram/message
Authorization: Bearer YOUR_AUTH_TOKEN
```
```json
{
  "recipient_id": "instagram_user_id",
  "message": "Hello! Test message from Kortana AI.",
  "message_type": "text"
}
```

**Test Statistics:**
```
GET http://kortana-ai.test/api/v1/social/stats?platform=instagram
Authorization: Bearer YOUR_AUTH_TOKEN
```

- [ ] Message sending endpoint works
- [ ] Statistics endpoint returns data
- [ ] No errors in Laravel logs

### Step 9: Live Testing
- [ ] Send DM to your Instagram Business account
- [ ] Check `tail -f storage/logs/laravel.log` for webhook activity
- [ ] Verify automatic response is sent
- [ ] Check database for stored messages

## ✅ Production Readiness

### Step 10: HTTPS Setup (for production)
- [ ] Install ngrok: `ngrok http 80 --host-header=kortana-ai.test`
- [ ] Update webhook URL to ngrok HTTPS URL
- [ ] Test webhook with HTTPS URL

### Step 11: Monitoring Setup
- [ ] Set up log monitoring
- [ ] Configure error alerts
- [ ] Test rate limiting
- [ ] Implement queue jobs for message sending

## 🚨 Common Issues & Solutions

**Webhook Verification Fails:**
- Check verify token matches exactly
- Ensure endpoint returns plain text challenge

**Access Token Invalid:**
- Regenerate long-lived token
- Check all required permissions granted
- Verify page-Instagram connection

**Message Sending Fails:**
- User must message your page first (24-hour window)
- Check Instagram Business Account ID
- Verify access token permissions

**No Webhook Received:**
- Check webhook subscription in Meta console
- Use ngrok for HTTPS testing
- Monitor Laravel logs for errors

## 📞 Support Resources

- **Meta Developer Docs:** https://developers.facebook.com/docs/instagram-api
- **Instagram Messaging:** https://developers.facebook.com/docs/messenger-platform/instagram
- **Graph API Explorer:** https://developers.facebook.com/tools/explorer/
- **Laravel Logs:** `tail -f storage/logs/laravel.log`

---

**Total Setup Time: ~45 minutes**

Once completed, you'll have a fully functional Instagram messaging integration with webhook support, message sending capabilities, and analytics tracking! 