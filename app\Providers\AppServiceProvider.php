<?php

namespace App\Providers;

use Illuminate\Database\Events\MigrationsStarted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Bind repositories
        $this->app->bind(
            \App\Repositories\User\UserRepositoryInterface::class,
            \App\Repositories\User\UserRepository::class
        );
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Check for pgvector extension before migrations
        Event::listen(MigrationsStarted::class, function () {
            $this->checkPgVectorExtension();
        });
    }

    /**
     * Check if pgvector extension is available when using PostgreSQL
     */
    private function checkPgVectorExtension(): void
    {
        $connection = config('database.default');
        $config     = config("database.connections.{$connection}");

        // Only check for PostgreSQL connections with pgvector requirement
        if (isset($config['driver']) &&
            $config['driver'] === 'pgsql' &&
            isset($config['pgvector_required']) &&
            $config['pgvector_required']) {

            try {
                $extensions = DB::select("SELECT * FROM pg_available_extensions WHERE name = 'vector'");

                if (empty($extensions)) {
                    throw new \Exception(
                        "pgvector extension is required but not available in this PostgreSQL installation. " .
                        "Please install pgvector extension or set PGVECTOR_REQUIRED=false in your .env file."
                    );
                }

                // Check if extension is installed
                $installed = DB::select("SELECT * FROM pg_extension WHERE extname = 'vector'");

                if (empty($installed)) {
                    // Try to install the extension
                    try {
                        DB::statement('CREATE EXTENSION IF NOT EXISTS vector');
                    } catch (\Exception $e) {
                        throw new \Exception(
                            "pgvector extension is available but could not be installed. " .
                            "Please manually run 'CREATE EXTENSION vector;' in your database or " .
                            "ensure your database user has the necessary privileges."
                        );
                    }
                }
            } catch (\Exception $e) {
                if (strpos($e->getMessage(), 'pgvector') !== false) {
                    throw $e;
                }

                // Re-throw with more context for other database errors
                throw new \Exception(
                    "Failed to check pgvector extension: " . $e->getMessage() .
                    " Please ensure your database is accessible and pgvector is properly installed."
                );
            }
        }
    }
}
