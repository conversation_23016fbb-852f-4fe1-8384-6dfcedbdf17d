<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\KnowledgeUploadRequest;
use App\Repositories\KnowledgeRepository;
use App\Services\KnowledgeService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class KnowledgeUploadController extends Controller
{
    public function __construct(
        private readonly KnowledgeService $knowledgeService,
        private readonly KnowledgeRepository $knowledgeRepository
    ) {}

    /**
     * Upload and process a knowledge file
     */
    public function upload(KnowledgeUploadRequest $request): JsonResponse
    {
        try {
            $fileData = $request->getFileData();
            $fileInfo = $request->getFileInfo();

            Log::info('Starting knowledge file upload', [
                'file_name'   => $fileInfo['original_name'],
                'file_size'   => $fileInfo['human_size'],
                'uploaded_by' => $fileData['uploaded_by'],
            ]);

            $knowledgeFile = $this->knowledgeService->uploadFile(
                $fileData['file'],
                $fileData['uploaded_by']
            );

            return response()->json([
                'status'  => 'success',
                'message' => 'File uploaded and processing started',
                'data'    => [
                    'id'          => $knowledgeFile->id,
                    'file_name'   => $knowledgeFile->original_name,
                    'status'      => $knowledgeFile->status,
                    'type'        => $knowledgeFile->type,
                    'size'        => $knowledgeFile->human_readable_size,
                    'uploaded_at' => $knowledgeFile->created_at,
                ],
            ], 201);

        } catch (Exception $e) {
            Log::error('Knowledge file upload failed', [
                'error'     => $e->getMessage(),
                'file_info' => $request->getFileInfo(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get all knowledge files
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->input('per_page', 15);
            $files   = $this->knowledgeRepository->getAllFiles($perPage);

            return response()->json([
                'status' => 'success',
                'data'   => [
                    'files'      => $files->items(),
                    'pagination' => [
                        'current_page' => $files->currentPage(),
                        'last_page'    => $files->lastPage(),
                        'per_page'     => $files->perPage(),
                        'total'        => $files->total(),
                    ],
                ],
            ]);

        } catch (Exception $e) {
            return response()->json([
                'status'  => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get a specific knowledge file
     */
    public function show(int $id): JsonResponse
    {
        try {
            $file = $this->knowledgeRepository->findFileById($id);

            if (!$file) {
                return response()->json([
                    'status'  => 'error',
                    'message' => 'Knowledge file not found',
                ], 404);
            }

            $chunks = $this->knowledgeRepository->getChunksForFile($id);

            return response()->json([
                'status' => 'success',
                'data'   => [
                    'file'   => [
                        'id'            => $file->id,
                        'original_name' => $file->original_name,
                        'type'          => $file->type,
                        'status'        => $file->status,
                        'size'          => $file->human_readable_size,
                        'uploaded_at'   => $file->created_at,
                        'error_message' => $file->error_message,
                    ],
                    'chunks' => [
                        'count' => $chunks->count(),
                        'items' => $chunks->map(function ($chunk) {
                            return [
                                'id'              => $chunk->id,
                                'chunk_index'     => $chunk->chunk_index,
                                'content_preview' => substr($chunk->content, 0, 200) . '...',
                                'content_length'  => strlen($chunk->content),
                            ];
                        }),
                    ],
                ],
            ]);

        } catch (Exception $e) {
            return response()->json([
                'status'  => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a knowledge file
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $file = $this->knowledgeRepository->findFileById($id);

            if (!$file) {
                return response()->json([
                    'status'  => 'error',
                    'message' => 'Knowledge file not found',
                ], 404);
            }

            $deleted = $this->knowledgeService->deleteFile($file);

            if ($deleted) {
                return response()->json([
                    'status'  => 'success',
                    'message' => 'Knowledge file deleted successfully',
                ]);
            } else {
                return response()->json([
                    'status'  => 'error',
                    'message' => 'Failed to delete knowledge file',
                ], 500);
            }

        } catch (Exception $e) {
            return response()->json([
                'status'  => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Search knowledge base
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|max:500',
            'limit' => 'sometimes|integer|min:1|max:20',
        ]);

        try {
            $query = $request->input('query');
            $limit = $request->input('limit', 5);

            $results = $this->knowledgeService->searchKnowledge($query, $limit);

            return response()->json([
                'status' => 'success',
                'data'   => [
                    'query'         => $query,
                    'results_count' => count($results),
                    'results'       => $results,
                ],
            ]);

        } catch (Exception $e) {
            Log::error('Knowledge search failed', [
                'query' => $request->input('query'),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get knowledge base statistics
     */
    public function stats(): JsonResponse
    {
        try {
            $stats = $this->knowledgeService->getStats();

            return response()->json([
                'status' => 'success',
                'data'   => $stats,
            ]);

        } catch (Exception $e) {
            return response()->json([
                'status'  => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reprocess a failed file
     */
    public function reprocess(int $id): JsonResponse
    {
        try {
            $file = $this->knowledgeRepository->findFileById($id);

            if (!$file) {
                return response()->json([
                    'status'  => 'error',
                    'message' => 'Knowledge file not found',
                ], 404);
            }

            if ($file->status !== 'failed') {
                return response()->json([
                    'status'  => 'error',
                    'message' => 'File is not in failed status',
                ], 400);
            }

            // Reset file status and reprocess
            $this->knowledgeRepository->updateFile($file, [
                'status'        => 'processing',
                'error_message' => null,
            ]);

            $this->knowledgeService->processFile($file);

            return response()->json([
                'status'  => 'success',
                'message' => 'File reprocessing started',
            ]);

        } catch (Exception $e) {
            Log::error('File reprocessing failed', [
                'file_id' => $id,
                'error'   => $e->getMessage(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}