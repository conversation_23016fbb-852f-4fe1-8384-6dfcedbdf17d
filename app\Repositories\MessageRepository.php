<?php

declare (strict_types = 1);

namespace App\Repositories;

use App\Models\Message;
use App\Models\MetaToken;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

final class MessageRepository
{
    public function __construct(
        private readonly Message $model
    ) {}

    public function create(array $data): Message
    {
        return $this->model->create($data);
    }

    public function findById(int $id): ?Message
    {
        return $this->model->find($id);
    }

    public function findByMessageId(string $messageId): ?Message
    {
        return $this->model->where('message_id', $messageId)->first();
    }

    public function update(Message $message, array $data): Message
    {
        $message->update($data);
        return $message->fresh();
    }

    public function delete(Message $message): bool
    {
        return $message->delete();
    }

    /**
     * Get recent messages for a conversation with context
     */
    public function getConversationContext(string $conversationId, int $limit = 10): Collection
    {
        return $this->model->forConversation($conversationId, $limit)->get();
    }

    /**
     * Get the last message in a conversation
     */
    public function getLastMessage(string $conversationId): ?Message
    {
        return $this->model->where('conversation_id', $conversationId)
            ->orderBy('sent_at', 'desc')
            ->first();
    }

    /**
     * Get pending messages for processing
     */
    public function getPendingMessages(): Collection
    {
        return $this->model->pending()
            ->orderBy('sent_at', 'asc')
            ->get();
    }

    /**
     * Get messages by sender with pagination
     */
    public function getMessagesBySender(string $senderId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->where('sender_id', $senderId)
            ->orderBy('sent_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Mark message as replied with AI response
     */
    public function markAsReplied(Message $message, string $reply): Message
    {
        return $this->update($message, [
            'status' => 'replied',
            'reply'  => $reply,
        ]);
    }

    /**
     * Mark message as typing (for status updates)
     */
    public function markAsTyping(Message $message): Message
    {
        return $this->update($message, ['status' => 'typing']);
    }

    /**
     * Get conversation messages ordered by time for context building
     */
    public function getConversationHistory(string $conversationId, int $limit = 20): Collection
    {
        return $this->model->where('conversation_id', $conversationId)
            ->orderBy('sent_at', 'asc')
            ->limit($limit)
            ->get();
    }

    /**
     * Check if conversation exists
     */
    public function conversationExists(string $conversationId): bool
    {
        return $this->model->where('conversation_id', $conversationId)->exists();
    }

    /**
     * Get message statistics for analytics
     */
    public function getMessageStats(): array
    {
        return [
            'total'    => $this->model->count(),
            'pending'  => $this->model->where('status', 'pending')->count(),
            'replied'  => $this->model->where('status', 'replied')->count(),
            'today'    => $this->model->whereDate('created_at', today())->count(),
            'incoming' => $this->model->where('direction', 'incoming')->count(),
            'outgoing' => $this->model->where('direction', 'outgoing')->count(),
        ];
    }

    /**
     * Create an incoming message from webhook data
     */
    public function createIncomingMessage(array $data, MetaToken $metaToken): Message
    {
        return $this->create([
            'message_id'      => $data['message_id'] ?? null,
            'sender_id'       => $data['sender_id'],
            'recipient_id'    => $data['recipient_id'],
            'conversation_id' => $data['sender_id'], // Use sender ID as conversation ID
            'message'         => $data['message'],
            'platform'        => $data['platform'] ?? 'instagram',
            'status'          => 'pending',
            'sent_at'         => now(),
            'timestamp'       => $data['timestamp'] ? date('Y-m-d H:i:s', $data['timestamp']) : now(),
            'direction'       => 'incoming',
            'meta_token_id'   => $metaToken->id,
            'meta_data'       => $data['meta_data'] ?? null,
        ]);
    }

    /**
     * Create an outgoing message (reply) from webhook data
     */
    public function createOutgoingMessage(array $data, Message $incomingMessage, MetaToken $metaToken): Message
    {
        return $this->create([
            'message_id'      => $data['message_id'] ?? null,
            'sender_id'       => $metaToken->instagram_business_id,
            'recipient_id'    => $incomingMessage->sender_id,
            'conversation_id' => $incomingMessage->conversation_id,
            'message'         => $data['message'],
            'platform'        => $incomingMessage->platform,
            'status'          => 'replied',
            'sent_at'         => now(),
            'timestamp'       => now(),
            'direction'       => 'outgoing',
            'meta_token_id'   => $metaToken->id,
            'in_reply_to'     => $incomingMessage->id,
            'meta_data'       => $data['meta_data'] ?? null,
        ]);
    }

    /**
     * Get messages for a specific Instagram Business Account
     */
    public function getMessagesForInstagramAccount(string $instagramBusinessId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->where(function ($query) use ($instagramBusinessId) {
            $query->where('sender_id', $instagramBusinessId)
                ->orWhere('recipient_id', $instagramBusinessId);
        })
            ->where('platform', 'instagram')
            ->orderBy('timestamp', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get conversations for a specific Instagram Business Account
     */
    public function getConversationsForInstagramAccount(string $instagramBusinessId, int $limit = 20): Collection
    {
        // Get unique conversation IDs where this Instagram account is involved
        $conversationIds = $this->model
            ->where('platform', 'instagram')
            ->where(function ($query) use ($instagramBusinessId) {
                $query->where('sender_id', $instagramBusinessId)
                    ->orWhere('recipient_id', $instagramBusinessId);
            })
            ->distinct()
            ->pluck('conversation_id');

        // For each conversation, get the most recent message
        $conversations = collect();
        foreach ($conversationIds as $conversationId) {
            $latestMessage = $this->model
                ->where('conversation_id', $conversationId)
                ->orderBy('timestamp', 'desc')
                ->first();

            if ($latestMessage) {
                $conversations->push($latestMessage);
            }
        }

        return $conversations->take($limit);
    }
}
