<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if knowledge_chunks table exists
        if (Schema::hasTable('knowledge_chunks')) {
            // Check if embedding column exists as vector type
            $hasVectorColumn = false;
            try {
                $columns         = DB::select("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'knowledge_chunks' AND column_name = 'embedding'");
                $hasVectorColumn = !empty($columns) && ($columns[0]->data_type === 'USER-DEFINED' || $columns[0]->data_type === 'vector');
            } catch (Exception $e) {
                // If we can't check, assume we need to add the column
            }

            if ($hasVectorColumn) {
                // Drop vector column and indexes if they exist
                try {
                    DB::statement('DROP INDEX IF EXISTS knowledge_chunks_embedding_idx');
                    DB::statement('ALTER TABLE knowledge_chunks DROP COLUMN IF EXISTS embedding');
                } catch (Exception $e) {
                    // Ignore errors if column doesn't exist
                }
            }

            // Add embedding as text column
            Schema::table('knowledge_chunks', function (Blueprint $table) {
                if (!Schema::hasColumn('knowledge_chunks', 'embedding')) {
                    $table->text('embedding')->nullable();
                }
            });
        } else {
            // Create the table from scratch without vector dependencies
            Schema::create('knowledge_chunks', function (Blueprint $table) {
                $table->id();
                $table->text('content');
                $table->text('embedding')->nullable();
                $table->unsignedBigInteger('file_id');
                $table->integer('chunk_index');
                $table->json('metadata')->nullable();
                $table->timestamps();

                $table->foreign('file_id')->references('id')->on('knowledge_files')->onDelete('cascade');
                $table->index(['file_id', 'chunk_index']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is a fix, so we don't reverse it
        // The original migration should handle the down case
    }
};