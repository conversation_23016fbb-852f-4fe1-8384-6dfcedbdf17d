<?php

declare (strict_types = 1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class SocialMediaMessageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'platform'              => ['required', 'string', Rule::in(['instagram', 'facebook'])],
            'recipient_id'          => ['required', 'string'],
            'message'               => ['required', 'string', 'max:2000'],
            'message_type'          => ['sometimes', 'string', Rule::in(['text', 'image', 'video'])],
            'media_url'             => ['sometimes', 'string', 'url'],
            'metadata'              => ['sometimes', 'array'],
            'metadata.campaign_id'  => 'sometimes|string|max:255',
            'metadata.tags'         => 'sometimes|array',
            'metadata.tags.*'       => 'string|max:50',
            'metadata.priority'     => 'sometimes|string|in:low,normal,high,urgent',
            'metadata.scheduled_at' => 'sometimes|date|after:now',
        ];
    }

    public function messages(): array
    {
        return [
            'platform.required'     => 'Platform is required.',
            'platform.in'           => 'Platform must be one of: instagram, facebook.',
            'recipient_id.required' => 'Recipient ID is required.',
            'message.required'      => 'Message content is required.',
            'message.max'           => 'Message must not exceed 2000 characters.',
        ];
    }

    public function getPlatform(): string
    {
        return $this->input('platform');
    }

    public function getRecipientId(): string
    {
        return $this->input('recipient_id');
    }

    public function getMessage(): string
    {
        return $this->input('message');
    }

    public function getMessageType(): string
    {
        return $this->input('message_type', 'text');
    }

    public function getMetadata(): array
    {
        return $this->input('metadata', []);
    }

    public function isScheduled(): bool
    {
        return $this->has('metadata.scheduled_at');
    }

    public function isReply(): bool
    {
        return $this->has('reply_to_message_id');
    }

    public function hasMedia(): bool
    {
        return $this->has('media_url') && $this->getMessageType() !== 'text';
    }
}
