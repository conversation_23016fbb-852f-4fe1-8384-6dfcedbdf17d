<?php

declare (strict_types = 1);

namespace App\Services;

use App\Models\KnowledgeFile;
use App\Repositories\KnowledgeRepository;
use Exception;
use GeminiAPI\Client as GeminiClient;
use GeminiAPI\Resources\ModelName;
use GeminiAPI\Resources\Parts\TextPart;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

final class KnowledgeService
{
    private const CHUNK_SIZE    = 1000; // characters per chunk
    private const CHUNK_OVERLAP = 200; // overlap between chunks

    private GeminiClient $geminiClient;
    private ?OllamaService $ollamaService;
    private bool $useOllama;

    public function __construct(
        private readonly KnowledgeRepository $knowledgeRepository
    ) {
        $this->geminiClient = new GeminiClient(config('services.gemini.api_key'));

        // Initialize Ollama service if enabled
        $this->useOllama     = config('services.ollama.enabled', false);
        $this->ollamaService = $this->useOllama ? app(OllamaService::class) : null;

        // Check if Gemini is enabled via env variable
        $geminiEnabled = config('services.gemini.enabled', true);

        // Check if Ollama is actually available
        if ($this->useOllama && $this->ollamaService) {
            try {
                $this->useOllama = $this->ollamaService->isAvailable();
                if (!$this->useOllama) {
                    if (!$geminiEnabled) {
                        Log::error('Ollama service is not available and Gemini is not enabled. The application will not function correctly.');
                    } else {
                        Log::warning('Ollama service is enabled but not available. Falling back to Gemini for embeddings.');
                    }
                }
            } catch (Exception $e) {
                $this->useOllama = false;
                if (!$geminiEnabled) {
                    Log::error('Failed to connect to Ollama service and Gemini is not enabled. The application will not function correctly.', [
                        'error' => $e->getMessage(),
                    ]);
                } else {
                    Log::warning('Failed to connect to Ollama service. Falling back to Gemini for embeddings.', [
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        // Force use of Ollama if Gemini is not enabled
        if (!$geminiEnabled) {
            $this->useOllama = true;
            if (!$this->ollamaService || !$this->ollamaService->isAvailable()) {
                Log::error('Gemini is not enabled but Ollama is not available. Vector embeddings will not work.');
            }
        }
    }

    /**
     * Upload and process knowledge file
     */
    public function uploadFile(UploadedFile $file, int $uploadedBy): KnowledgeFile
    {
        try {
            DB::beginTransaction();

            // Validate file
            $this->validateFile($file);

            // Store file
            $fileName = $this->generateUniqueFileName($file);
            $filePath = $file->storeAs('knowledge', $fileName, 'private');

            // Create file record
            $knowledgeFile = $this->knowledgeRepository->createFile([
                'file_name'     => $fileName,
                'original_name' => $file->getClientOriginalName(),
                'uploaded_by'   => $uploadedBy,
                'type'          => $this->getFileType($file),
                'file_path'     => $filePath,
                'file_size'     => $file->getSize(),
                'mime_type'     => $file->getMimeType(),
                'status'        => 'processing',
            ]);

            DB::commit();

            // Process file asynchronously (in a real app, this would be a queued job)
            $this->processFile($knowledgeFile);

            return $knowledgeFile;

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to upload knowledge file', [
                'error'     => $e->getMessage(),
                'file_name' => $file->getClientOriginalName(),
            ]);

            throw $e;
        }
    }

    /**
     * Process uploaded file: extract text, chunk, and embed
     */
    public function processFile(KnowledgeFile $file): void
    {
        try {
            // Extract text from file
            $text = $this->extractTextFromFile($file);

            // Split text into chunks
            $chunks = $this->chunkText($text);

            // Process chunks with embeddings
            $this->processChunks($file, $chunks);

            // Mark file as completed
            $this->knowledgeRepository->markFileAsCompleted($file);

            Log::info('Successfully processed knowledge file', [
                'file_id'        => $file->id,
                'chunks_created' => count($chunks),
            ]);

        } catch (Exception $e) {
            $this->knowledgeRepository->markFileAsFailed($file, $e->getMessage());

            Log::error('Failed to process knowledge file', [
                'file_id' => $file->id,
                'error'   => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Extract text content from various file types
     */
    private function extractTextFromFile(KnowledgeFile $file): string
    {
        $filePath = Storage::disk('private')->path($file->file_path);

        switch ($file->type) {
            case 'txt':
                return file_get_contents($filePath);

            case 'pdf':
                return $this->extractTextFromPDF($filePath);

            case 'doc':
            case 'docx':
                return $this->extractTextFromWord($filePath);

            default:
                throw new Exception("Unsupported file type: {$file->type}");
        }
    }

    /**
     * Extract text from PDF using smalot/pdfparser
     */
    private function extractTextFromPDF(string $filePath): string
    {
        try {
            $parser = new \Smalot\PdfParser\Parser();
            $pdf    = $parser->parseFile($filePath);
            $text   = $pdf->getText();

            if (empty(trim($text))) {
                throw new Exception('No text content found in PDF');
            }

            return $text;
        } catch (Exception $e) {
            throw new Exception("Failed to extract text from PDF: " . $e->getMessage());
        }
    }

    /**
     * Extract text from Word documents using phpoffice/phpword
     */
    private function extractTextFromWord(string $filePath): string
    {
        try {
            $phpWord = \PhpOffice\PhpWord\IOFactory::load($filePath);
            $text    = '';

            foreach ($phpWord->getSections() as $section) {
                $text .= $this->extractTextFromElements($section->getElements());
            }

            if (empty(trim($text))) {
                throw new Exception('No text content found in Word document');
            }

            return $text;
        } catch (Exception $e) {
            throw new Exception("Failed to extract text from Word document: " . $e->getMessage());
        }
    }

    /**
     * Recursively extract text from Word document elements
     */
    private function extractTextFromElements(array $elements): string
    {
        $text = '';

        foreach ($elements as $element) {
            // Handle different element types
            if ($element instanceof \PhpOffice\PhpWord\Element\Text) {
                $text .= $element->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                foreach ($element->getElements() as $subElement) {
                    if ($subElement instanceof \PhpOffice\PhpWord\Element\Text) {
                        $text .= $subElement->getText() . ' ';
                    }
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\TextBreak) {
                $text .= "\n";
            } elseif (method_exists($element, 'getElements')) {
                // Handle containers like tables, lists, etc.
                $text .= $this->extractTextFromElements($element->getElements());
            } elseif (method_exists($element, 'getText')) {
                // Fallback for other text elements
                $text .= $element->getText() . ' ';
            }
        }

        return $text;
    }

    /**
     * Split text into overlapping chunks
     */
    private function chunkText(string $text): array
    {
        $text = trim($text);
        if (empty($text)) {
            throw new Exception('No text content found in file');
        }

        $chunks     = [];
        $textLength = strlen($text);
        $position   = 0;
        $chunkIndex = 0;

        while ($position < $textLength) {
            $chunkEnd = min($position + self::CHUNK_SIZE, $textLength);

            // Try to break at sentence or paragraph boundary
            if ($chunkEnd < $textLength) {
                $lastPeriod  = strrpos(substr($text, $position, self::CHUNK_SIZE), '.');
                $lastNewline = strrpos(substr($text, $position, self::CHUNK_SIZE), "\n");

                $breakPoint = max($lastPeriod, $lastNewline);
                if ($breakPoint !== false && $breakPoint > self::CHUNK_SIZE * 0.7) {
                    $chunkEnd = $position + $breakPoint + 1;
                }
            }

            $chunk = trim(substr($text, $position, $chunkEnd - $position));

            if (!empty($chunk)) {
                $chunks[] = [
                    'content'     => $chunk,
                    'chunk_index' => $chunkIndex++,
                    'metadata'    => [
                        'start_position' => $position,
                        'end_position'   => $chunkEnd,
                        'length'         => strlen($chunk),
                    ],
                ];
            }

            $position = $chunkEnd - self::CHUNK_OVERLAP;
            if ($position < 0) {
                $position = $chunkEnd;
            }

        }

        return $chunks;
    }

    /**
     * Process chunks: generate embeddings and store
     */
    private function processChunks(KnowledgeFile $file, array $chunks): void
    {
        foreach ($chunks as $chunkData) {
            try {
                // Generate embedding for chunk
                $embedding = $this->generateEmbedding($chunkData['content']);

                // Store chunk with embedding
                $this->knowledgeRepository->createChunk([
                    'content'     => $chunkData['content'],
                    'embedding'   => $embedding,
                    'file_id'     => $file->id,
                    'chunk_index' => $chunkData['chunk_index'],
                    'metadata'    => $chunkData['metadata'],
                ]);

            } catch (Exception $e) {
                Log::warning('Failed to process chunk', [
                    'file_id'     => $file->id,
                    'chunk_index' => $chunkData['chunk_index'],
                    'error'       => $e->getMessage(),
                ]);

                // Continue processing other chunks even if one fails
                continue;
            }
        }
    }

    /**
     * Generate embedding using available service (Ollama or Gemini)
     */
    private function generateEmbedding(string $text): array
    {
        try {
            // Try Ollama first if enabled
            if ($this->useOllama && $this->ollamaService) {
                try {
                    return $this->ollamaService->generateEmbedding($text);
                } catch (Exception $e) {
                    Log::warning('Failed to generate Ollama embedding, falling back to Gemini', [
                        'error' => $e->getMessage(),
                    ]);
                    // Fall through to Gemini
                }
            }

            // Use Gemini as primary or fallback
            $response = $this->geminiClient->embeddingModel(ModelName::EMBEDDING_001)
                ->embedContent(new TextPart($text));

            return $response->embedding->values;
        } catch (Exception $e) {
            Log::error('Failed to generate embedding', [
                'error' => $e->getMessage(),
                'text'  => substr($text, 0, 100),
            ]);
            throw new Exception('Failed to generate embedding: ' . $e->getMessage());
        }
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file): void
    {
        $allowedTypes = ['pdf', 'txt', 'doc', 'docx'];
        $maxSize      = 10 * 1024 * 1024; // 10MB

        if ($file->getSize() > $maxSize) {
            throw new Exception('File size exceeds maximum allowed size of 10MB');
        }

        $fileType = $this->getFileType($file);
        if (!in_array($fileType, $allowedTypes)) {
            throw new Exception('Unsupported file type. Allowed types: ' . implode(', ', $allowedTypes));
        }
    }

    /**
     * Determine file type from extension
     */
    private function getFileType(UploadedFile $file): string
    {
        $extension = strtolower($file->getClientOriginalExtension());

        $typeMap = [
            'pdf'  => 'pdf',
            'txt'  => 'txt',
            'doc'  => 'doc',
            'docx' => 'docx',
        ];

        return $typeMap[$extension] ?? 'txt';
    }

    /**
     * Generate unique file name
     */
    private function generateUniqueFileName(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $baseName  = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $timestamp = now()->format('Y-m-d_H-i-s');
        $hash      = substr(md5($baseName . $timestamp), 0, 8);

        return "{$baseName}_{$timestamp}_{$hash}.{$extension}";
    }

    /**
     * Delete knowledge file and all associated chunks
     */
    public function deleteFile(KnowledgeFile $file): bool
    {
        try {
            DB::beginTransaction();

            // Delete file from storage
            Storage::disk('private')->delete($file->file_path);

            // Delete chunks (cascade will handle this, but explicit is better)
            $this->knowledgeRepository->deleteChunksForFile($file->id);

            // Delete file record
            $this->knowledgeRepository->deleteFile($file);

            DB::commit();

            Log::info('Successfully deleted knowledge file', [
                'file_id'   => $file->id,
                'file_name' => $file->original_name,
            ]);

            return true;

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete knowledge file', [
                'file_id' => $file->id,
                'error'   => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Search knowledge base for similar content
     */
    public function searchKnowledge(string $query, int $limit = 5): array
    {
        try {
            // Generate embedding for query
            $embedding = $this->generateEmbedding($query);

            // Find similar chunks
            $similarChunks = $this->knowledgeRepository->findSimilarChunks($embedding, $limit);

            return $similarChunks->map(function ($chunk) {
                return [
                    'content'     => $chunk->content,
                    'file_name'   => $chunk->knowledgeFile->original_name,
                    'distance'    => $chunk->distance,
                    'chunk_index' => $chunk->chunk_index,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::error('Failed to search knowledge base', [
                'query' => $query,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Get knowledge base statistics
     */
    public function getStats(): array
    {
        return $this->knowledgeRepository->getKnowledgeStats();
    }
}
