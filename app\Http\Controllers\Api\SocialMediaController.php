<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SocialMediaMessageRequest;
use App\Http\Resources\SocialMediaMessageResource;
use App\Http\Resources\SocialMediaStatsResource;
use App\Repositories\SocialMediaRepository;
use App\Services\FacebookService;
use App\Services\InstagramService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class SocialMediaController extends Controller
{
    public function __construct(
        private readonly SocialMediaRepository $socialMediaRepository,
        private readonly InstagramService $instagramService,
        private readonly FacebookService $facebookService
    ) {}

    public function sendMessage(SocialMediaMessageRequest $request): JsonResponse
    {
        try {
            $platform    = $request->getPlatform();
            $recipientId = $request->getRecipientId();
            $message     = $request->getMessage();

            $success = match ($platform) {
                'instagram' => $this->instagramService->sendMessage($recipientId, $message),
                'facebook' => $this->facebookService->sendMessage($recipientId, $message),
                default => false,
            };

            if (!$success) {
                return response()->json([
                    'status'  => 'error',
                    'message' => 'Failed to send message',
                ], 500);
            }

            return response()->json([
                'status'  => 'success',
                'message' => 'Message sent successfully',
                'data'    => [
                    'platform'       => $platform,
                    'recipient_id'   => $recipientId,
                    'message_length' => strlen($message),
                    'sent_at'        => now()->toISOString(),
                ],
            ]);

        } catch (Exception $e) {
            Log::error('Social media message sending failed', [
                'error'        => $e->getMessage(),
                'request_data' => $request->validated(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => 'Internal server error',
            ], 500);
        }
    }

    public function getPlatformStats(string $platform): JsonResponse
    {
        try {
            $stats             = $this->socialMediaRepository->getPlatformStats($platform);
            $engagementMetrics = $this->socialMediaRepository->getPlatformEngagementMetrics($platform);

            $combinedStats = array_merge($stats, $engagementMetrics, ['platform' => $platform]);

            return response()->json([
                'status' => 'success',
                'data'   => new SocialMediaStatsResource($combinedStats),
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get platform statistics', [
                'platform' => $platform,
                'error'    => $e->getMessage(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => 'Failed to retrieve platform statistics',
            ], 500);
        }
    }

    public function getMessages(Request $request, string $platform): JsonResponse
    {
        $request->validate([
            'per_page' => 'sometimes|integer|min:1|max:100',
        ]);

        try {
            $perPage  = $request->input('per_page', 15);
            $messages = $this->socialMediaRepository->getMessagesByPlatform($platform, $perPage);

            return response()->json([
                'status' => 'success',
                'data'   => [
                    'platform'   => $platform,
                    'messages'   => SocialMediaMessageResource::collection($messages->items()),
                    'pagination' => [
                        'current_page' => $messages->currentPage(),
                        'last_page'    => $messages->lastPage(),
                        'per_page'     => $messages->perPage(),
                        'total'        => $messages->total(),
                    ],
                ],
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get messages by platform', [
                'platform' => $platform,
                'error'    => $e->getMessage(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => 'Failed to retrieve messages',
            ], 500);
        }
    }

    public function getConversationHistory(Request $request, string $platform): JsonResponse
    {
        $request->validate([
            'conversation_id' => 'required|string',
            'limit'           => 'sometimes|integer|min:1|max:50',
        ]);

        try {
            $conversationId = $request->input('conversation_id');
            $limit          = $request->input('limit', 20);

            $messages = $this->socialMediaRepository->getConversationHistoryByPlatform(
                $conversationId,
                $platform,
                $limit
            );

            return response()->json([
                'status' => 'success',
                'data'   => [
                    'platform'        => $platform,
                    'conversation_id' => $conversationId,
                    'messages'        => SocialMediaMessageResource::collection($messages),
                ],
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get conversation history', [
                'platform' => $platform,
                'error'    => $e->getMessage(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => 'Failed to retrieve conversation history',
            ], 500);
        }
    }

    public function getActiveConversations(string $platform): JsonResponse
    {
        try {
            $conversations = $this->socialMediaRepository->getActiveConversationsByPlatform($platform);

            return response()->json([
                'status' => 'success',
                'data'   => [
                    'platform'      => $platform,
                    'conversations' => $conversations,
                ],
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get active conversations', [
                'platform' => $platform,
                'error'    => $e->getMessage(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => 'Failed to retrieve active conversations',
            ], 500);
        }
    }

    public function getPendingMessages(string $platform): JsonResponse
    {
        try {
            $messages = $this->socialMediaRepository->getPendingMessagesByPlatform($platform);

            return response()->json([
                'status' => 'success',
                'data'   => [
                    'platform'         => $platform,
                    'pending_messages' => SocialMediaMessageResource::collection($messages),
                ],
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get pending messages', [
                'platform' => $platform,
                'error'    => $e->getMessage(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => 'Failed to retrieve pending messages',
            ], 500);
        }
    }

    public function getHourlyDistribution(string $platform): JsonResponse
    {
        try {
            $distribution = $this->socialMediaRepository->getHourlyMessageDistribution($platform);

            return response()->json([
                'status' => 'success',
                'data'   => [
                    'platform'            => $platform,
                    'hourly_distribution' => $distribution,
                ],
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get hourly distribution', [
                'platform' => $platform,
                'error'    => $e->getMessage(),
            ]);

            return response()->json([
                'status'  => 'error',
                'message' => 'Failed to retrieve hourly distribution',
            ], 500);
        }
    }
}