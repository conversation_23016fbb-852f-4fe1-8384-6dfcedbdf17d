<?php

declare (strict_types = 1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

final class KnowledgeUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Add authorization logic here if needed (e.g., check if user is admin)
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file'        => [
                'required',
                'file',
                'max:10240', // 10MB max
                'mimes:pdf,txt,doc,docx',
            ],
            'uploaded_by' => ['sometimes', 'integer', 'exists:users,id'],
            'description' => ['sometimes', 'string', 'max:500'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'file.required'      => 'A knowledge file is required.',
            'file.file'          => 'The uploaded item must be a valid file.',
            'file.max'           => 'The file size cannot exceed 10MB.',
            'file.mimes'         => 'The file must be of type: PDF, TXT, DOC, or DOCX.',
            'uploaded_by.exists' => 'The specified user does not exist.',
            'description.max'    => 'Description cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'file'        => 'knowledge file',
            'uploaded_by' => 'uploader',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default uploaded_by to authenticated user if not provided
        if (!$this->has('uploaded_by') && Auth::check()) {
            $this->merge([
                'uploaded_by' => Auth::id(),
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional custom validation
            if ($this->hasFile('file')) {
                $file = $this->file('file');

                // Check file content is not empty
                if ($file->getSize() < 10) {
                    $validator->errors()->add('file', 'The file appears to be empty or corrupted.');
                }

                // Check file extension matches MIME type
                $extension = strtolower($file->getClientOriginalExtension());
                $mimeType  = $file->getMimeType();

                $allowedMimes = [
                    'pdf'  => ['application/pdf'],
                    'txt'  => ['text/plain', 'text/x-plain'],
                    'doc'  => ['application/msword'],
                    'docx' => [
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    ],
                ];

                if (isset($allowedMimes[$extension]) &&
                    !in_array($mimeType, $allowedMimes[$extension])) {
                    $validator->errors()->add('file', 'File extension does not match file content.');
                }
            }
        });
    }

    /**
     * Get the processed file data for the service layer
     */
    public function getFileData(): array
    {
        $validated = $this->validated();

        return [
            'file'        => $validated['file'],
            'uploaded_by' => $validated['uploaded_by'] ?? Auth::id() ?? 1,
            'description' => $validated['description'] ?? null,
        ];
    }

    /**
     * Get file information
     */
    public function getFileInfo(): array
    {
        if (!$this->hasFile('file')) {
            return [];
        }

        $file = $this->file('file');

        return [
            'original_name' => $file->getClientOriginalName(),
            'size'          => $file->getSize(),
            'mime_type'     => $file->getMimeType(),
            'extension'     => $file->getClientOriginalExtension(),
            'human_size'    => $this->formatBytes($file->getSize()),
        ];
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}