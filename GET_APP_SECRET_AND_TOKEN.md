# How to Get App Secret and Access Token

## 🔐 Step 1: Get App Secret from Meta Developer Console

### Navigate to App Secret:

1. **Go to your Meta Developer Console**: https://developers.facebook.com/apps/
2. **Click on your app**: `kortana01` (ID: ****************)
3. **In the left sidebar**, click **"App settings"**
4. **Click "Basic"** (should be selected by default)
5. **Scroll down to find "App Secret"**
6. **Click "Show"** next to the App Secret field
7. **Enter your Facebook password** when prompted
8. **Copy the App Secret** (it will look like a long string of letters and numbers)

### Add to .env:

```env
META_APP_SECRET=your_copied_app_secret_here
```

---

## 🎫 Step 2: Generate Access Token

### Method 1: Using Graph API Explorer (Recommended)

#### Navigate to Graph API Explorer:

1. **Open new tab**: https://developers.facebook.com/tools/explorer/
2. **Select your app** from dropdown: `kortana01-IG` (****************)
3. **Click "Generate Access Token"** button
4. **Select permissions** (check these boxes):
    - ✅ `pages_show_list`
    - ✅ `pages_read_engagement`
    - ✅ `pages_manage_metadata`
    - ✅ `instagram_basic`
    - ✅ `instagram_manage_messages`
    - ✅ `instagram_manage_comments`
5. **Click "Generate Access Token"**
6. **Copy the generated token** (starts with something like `EAAS...`)

#### Exchange for Long-Lived Token:

```bash
# Replace YOUR_APP_SECRET and YOUR_SHORT_LIVED_TOKEN with actual values
curl -X GET "https://graph.facebook.com/v23.0/oauth/access_token" \
  -d "grant_type=fb_exchange_token" \
  -d "client_id=****************" \
  -d "client_secret=YOUR_APP_SECRET" \
  -d "fb_exchange_token=YOUR_SHORT_LIVED_TOKEN"
```

**Response will look like:**

```json
{
    "access_token": "EAAS...long_lived_token...",
    "token_type": "bearer",
    "expires_in": 5183944
}
```

### Method 2: Using Your Meta Console Token Generator

#### In your Meta Console:

1. **Go back to your app**: `kortana01`
2. **In left sidebar**, click **"Instagram"**
3. **Click "API setup with Instagram login"**
4. **Look for "Generate access tokens"** section
5. **Click "Generate token"** next to your Instagram account
6. **Copy the generated token**

---

## 🏢 Step 3: Get Instagram Business Account ID

### Get Your Facebook Pages:

```bash
# Use your long-lived token
curl -X GET "https://graph.facebook.com/v23.0/me/accounts" \
  -d "access_token=YOUR_LONG_LIVED_TOKEN"
```

**Response will show your pages:**

```json
{
    "data": [
        {
            "access_token": "page_access_token",
            "category": "Business",
            "name": "Your Page Name",
            "id": "YOUR_PAGE_ID",
            "tasks": ["MANAGE", "CREATE_CONTENT"]
        }
    ]
}
```

### Get Instagram Business Account Connected to Page:

```bash
# Use the PAGE_ID from above
curl -X GET "https://graph.facebook.com/v23.0/YOUR_PAGE_ID" \
  -d "fields=instagram_business_account" \
  -d "access_token=YOUR_LONG_LIVED_TOKEN"
```

**Response will show Instagram connection:**

```json
{
    "instagram_business_account": {
        "id": "YOUR_INSTAGRAM_BUSINESS_ID"
    },
    "id": "YOUR_PAGE_ID"
}
```

---

## 📝 Step 4: Update Your .env File

```env
# From Meta Console App Settings > Basic
META_APP_SECRET=your_actual_app_secret_from_step_1

# From Graph API Explorer (long-lived token)
META_PAGE_ACCESS_TOKEN=your_long_lived_token_from_step_2

# From Graph API calls
INSTAGRAM_BUSINESS_ACCOUNT_ID=your_instagram_business_id_from_step_3
INSTAGRAM_PAGE_ID=your_facebook_page_id_from_step_3
```

---

## 🧪 Step 5: Test Your Configuration

### Test API Connectivity:

```bash
# Test if your token works
curl -X GET "https://graph.facebook.com/v23.0/me?access_token=YOUR_LONG_LIVED_TOKEN"
```

### Test Instagram Account Access:

```bash
# Test Instagram Business account access
curl -X GET "https://graph.facebook.com/v23.0/YOUR_INSTAGRAM_BUSINESS_ID?fields=id,username&access_token=YOUR_LONG_LIVED_TOKEN"
```

### Clear Laravel Cache:

```bash
php artisan config:clear
php artisan cache:clear
```

---

## 🚨 Troubleshooting

### If App Secret is not showing:

-   Make sure you're logged in as the app owner
-   Try refreshing the page
-   Check if you have proper permissions for the app

### If Access Token generation fails:

-   Make sure your app is in "Development" mode
-   Check if Instagram product is properly added
-   Verify your Instagram account is added as a test user

### If Instagram Business Account ID is empty:

-   Ensure your Instagram account is converted to Business
-   Check if Instagram is properly connected to your Facebook page
-   Verify the Facebook page is linked to the Instagram Business account

### If permissions are denied:

-   Make sure you're using a Business Instagram account (not Personal)
-   Check if the Instagram account is properly connected to a Facebook page
-   Verify all required permissions are granted

---

## 📋 Quick Checklist

-   [ ] ✅ Got App Secret from Meta Console
-   [ ] ✅ Generated short-lived access token
-   [ ] ✅ Exchanged for long-lived token
-   [ ] ✅ Got Facebook Page ID
-   [ ] ✅ Got Instagram Business Account ID
-   [ ] ✅ Updated .env file
-   [ ] ✅ Cleared Laravel cache
-   [ ] ✅ Tested API connectivity

**Once you have all values, your .env should look like:**

```env
META_APP_ID=****************
META_APP_SECRET=abc123def456ghi789...
META_WEBHOOK_VERIFY_TOKEN=kortana_webhook_verify_2024
META_PAGE_ACCESS_TOKEN=EAAS...long_token...
INSTAGRAM_APP_ID=****************
INSTAGRAM_APP_SECRET=c154b2c349e4cadc9b48c
INSTAGRAM_BUSINESS_ACCOUNT_ID=*****************
INSTAGRAM_PAGE_ID=*****************
```

**Ready to proceed with webhook configuration once you have these values!**
