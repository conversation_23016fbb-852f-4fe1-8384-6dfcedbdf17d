<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->string('customer_name');
            $table->string('customer_id')->nullable();
            $table->date('appointment_date');
            $table->time('time_slot');
            $table->enum('status', ['available', 'booked', 'completed', 'cancelled'])->default('available');
            $table->text('notes')->nullable();
            $table->string('contact_info')->nullable();
            $table->timestamps();

            $table->index(['appointment_date', 'time_slot']);
            $table->index(['status', 'appointment_date']);
            $table->unique(['appointment_date', 'time_slot']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
