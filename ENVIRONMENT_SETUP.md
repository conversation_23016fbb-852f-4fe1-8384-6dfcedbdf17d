# Environment Setup Guide

This document describes all the environment variables required to configure the Kortana AI Instagram Chatbot.

## Required Environment Variables

Create a `.env` file in your project root with the following configuration:

```bash
# Application Configuration
APP_NAME="Kortana AI Instagram Chatbot"
APP_ENV=local
APP_KEY=base64:YOUR_GENERATED_APP_KEY_HERE
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

# Logging Configuration
LOG_CHANNEL=stack
LOG_STACK=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database Configuration (PostgreSQL with pgvector)
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=kortana_ai
DB_USERNAME=postgres
DB_PASSWORD=your_postgres_password

# Enable pgvector extension requirement
PGVECTOR_REQUIRED=true

# Session Configuration
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Cache Configuration
CACHE_STORE=redis
CACHE_PREFIX=

# Queue Configuration
QUEUE_CONNECTION=database

# Redis Configuration
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1

# Gemini AI Configuration (REQUIRED)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.0-flash
GEMINI_EMBEDDING_MODEL=text-embedding-004
GEMINI_MAX_TOKENS=1000
GEMINI_TEMPERATURE=0.7
GEMINI_TOP_P=0.9
GEMINI_TOP_K=40
GEMINI_ENABLED=true

# Ollama Configuration (Local LLM - OPTIONAL)
OLLAMA_ENABLED=false
OLLAMA_API_URL=http://localhost:11434
OLLAMA_MODEL=deepseek-r1:8b
OLLAMA_EMBEDDING_MODEL=nomic-embed-text
OLLAMA_MAX_TOKENS=600
OLLAMA_TEMPERATURE=0.7
OLLAMA_TOP_P=0.9
OLLAMA_TOP_K=40

# Meta Graph API Configuration (REQUIRED)
META_GRAPH_API_URL=https://graph.facebook.com/v23.0
META_WEBHOOK_VERIFY_TOKEN=your_unique_webhook_verify_token
META_APP_SECRET=your_meta_app_secret_from_facebook_developer
META_WEBHOOK_URL=https://your-domain.com/api/instagram/webhook

# Instagram Bot Configuration (REQUIRED)
INSTAGRAM_BUSINESS_ACCOUNT_ID=your_instagram_business_account_id
INSTAGRAM_PAGE_ACCESS_TOKEN=your_page_access_token_from_meta

# Chatbot Configuration
CHATBOT_RATE_LIMIT_PER_MINUTE=60
CHATBOT_MAX_CONTEXT_MESSAGES=20
CHATBOT_DEFAULT_RESPONSE="Hello! I'm the Kortana AI assistant. How can I help you today?"

# File Upload Configuration
MAX_UPLOAD_SIZE=10240
ALLOWED_FILE_TYPES=pdf,txt,doc,docx
KNOWLEDGE_CHUNK_SIZE=1000
KNOWLEDGE_CHUNK_OVERLAP=200

# Appointment Configuration
BUSINESS_HOURS_START=12:00
BUSINESS_HOURS_END=20:00
APPOINTMENT_DURATION=60
APPOINTMENT_TIMEZONE=UTC

# Vector Search Configuration
VECTOR_SIMILARITY_THRESHOLD=0.7
VECTOR_MAX_RESULTS=5
```

## Configuration Details

### Ollama Configuration (Optional)

If you want to use locally hosted Ollama models instead of Gemini API:

1. Install Ollama from [https://ollama.ai/](https://ollama.ai/)
2. Pull the required models:
    ```bash
    ollama pull deepseek-r1:8b
    ollama pull nomic-embed-text
    ```
3. Start the Ollama service:
    ```bash
    ollama serve
    ```
4. Update your `.env` file:
    ```bash
    OLLAMA_ENABLED=true
    OLLAMA_API_URL=http://localhost:11434
    OLLAMA_MODEL=deepseek-r1:8b
    OLLAMA_EMBEDDING_MODEL=nomic-embed-text
    ```

To use Ollama exclusively and disable Gemini API completely:

```bash
OLLAMA_ENABLED=true
GEMINI_ENABLED=false
```

The application will automatically fall back to Gemini if Ollama is not available or encounters an error, unless Gemini is explicitly set to disabled by setting GEMINI_ENABLED=false.

### Gemini API Configuration

1. Create a Google Cloud account and enable the Gemini API
2. Generate an API key from the Google Cloud Console
3. Update your `.env` file with the key:
    ```bash
    GEMINI_API_KEY=your_gemini_api_key_here
    ```

### Meta API Configuration

1. Create a Facebook Developer account
2. Create a new app with Instagram Messaging permissions
3. Configure the webhook for your app
4. Generate a page access token
5. Update your `.env` file with the required values

## Additional Configuration

### Database Setup

The application requires PostgreSQL with the pgvector extension for vector similarity search:

1. Install PostgreSQL 14+
2. Install the pgvector extension:
    ```sql
    CREATE EXTENSION vector;
    ```
3. Create the database:
    ```sql
    CREATE DATABASE kortana_ai;
    ```

### Redis Setup

Redis is used for caching and rate limiting:

1. Install Redis
2. Update your `.env` file with the Redis configuration

## Setup Instructions

### 1. Database Setup (PostgreSQL with pgvector)

1. Install PostgreSQL and the pgvector extension
2. Create a database: `createdb kortana_ai`
3. Install pgvector in your database:
    ```sql
    CREATE EXTENSION vector;
    ```

### 2. Gemini AI Setup

1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Generate an API key for Gemini
3. Set `GEMINI_API_KEY` in your `.env` file
4. Note: Gemini 2.0 Flash model is free with rate limits

### 3. Meta/Instagram API Setup

1. Create a Facebook App at [Meta for Developers](https://developers.facebook.com/)
2. Add Instagram Basic Display and Instagram Messaging products
3. Set up webhook URL: `https://your-domain.com/api/instagram/webhook`
4. Configure the following variables:
    - `META_WEBHOOK_VERIFY_TOKEN`: A random string you choose
    - `META_APP_SECRET`: From your Facebook app settings
    - `INSTAGRAM_PAGE_ACCESS_TOKEN`: Page access token with required permissions

### 4. Required Permissions

Your Instagram page access token must have these permissions:

-   `pages_messaging`
-   `pages_show_list`
-   `instagram_basic`
-   `instagram_manage_messages`

### 5. Installation Commands

```bash
# Install dependencies
composer install

# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate

# Clear config cache
php artisan config:clear

# Start the application
php artisan serve
```

### 6. Webhook Verification

Test your webhook endpoint:

```bash
# Verify webhook
curl -X GET "https://your-domain.com/api/instagram/webhook?hub.verify_token=your_webhook_verify_token&hub.challenge=test&hub.mode=subscribe"

# Test webhook status
curl -X GET "https://your-domain.com/api/instagram/webhook/status"
```

### 7. Knowledge Base Setup

Upload knowledge files via the API:

```bash
curl -X POST "https://your-domain.com/api/v1/knowledge/upload" \
  -F "file=@your-knowledge-file.pdf" \
  -F "type=pdf"
```

### 8. Testing the Chatbot

Test the AI response:

```bash
curl -X POST "https://your-domain.com/api/v1/chatbot/test" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "sender_id": "test_user"}'
```

## Security Notes

-   Never commit your `.env` file to version control
-   Use strong, unique tokens for webhook verification
-   Regularly rotate your API keys
-   Enable HTTPS in production
-   Use proper authentication for admin endpoints

## Troubleshooting

### Common Issues

1. **pgvector extension not found**

    - Ensure PostgreSQL has pgvector installed
    - Check database connection settings

2. **Gemini AI API errors**

    - Verify API key is valid and has proper permissions
    - Check rate limits (free tier has daily limits)
    - Ensure model name is correct (gemini-2.0-flash)

3. **Meta webhook verification fails**

    - Ensure webhook URL is accessible
    - Verify token matches exactly
    - Check SSL certificate

4. **File upload errors**
    - Check file size limits
    - Verify storage permissions
    - Ensure allowed file types are correct

For more help, check the Laravel logs at `storage/logs/laravel.log`.
