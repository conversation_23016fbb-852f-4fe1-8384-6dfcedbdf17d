<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

final class Appointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_name',
        'customer_id',
        'appointment_date',
        'time_slot',
        'status',
        'notes',
        'contact_info',
    ];

    protected $casts = [
        'appointment_date' => 'date',
        'time_slot'        => 'datetime:H:i',
        'status'           => 'string',
    ];

    public function getRouteKeyName(): string
    {
        return 'id';
    }
}
