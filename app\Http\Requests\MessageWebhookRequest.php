<?php

declare (strict_types = 1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class MessageWebhookRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Add webhook verification logic here if needed
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'object'                               => ['required', 'string', Rule::in(['page'])],
            'entry'                                => ['required', 'array', 'min:1'],
            'entry.*.id'                           => ['required', 'string'],
            'entry.*.time'                         => ['required', 'integer'],
            'entry.*.messaging'                    => ['required', 'array', 'min:1'],
            'entry.*.messaging.*.sender'           => ['required', 'array'],
            'entry.*.messaging.*.sender.id'        => ['required', 'string'],
            'entry.*.messaging.*.recipient'        => ['required', 'array'],
            'entry.*.messaging.*.recipient.id'     => ['required', 'string'],
            'entry.*.messaging.*.timestamp'        => ['required', 'integer'],
            'entry.*.messaging.*.message'          => ['sometimes', 'array'],
            'entry.*.messaging.*.message.mid'      => ['sometimes', 'string'],
            'entry.*.messaging.*.message.text'     => ['sometimes', 'string', 'max:2000'],
            'entry.*.messaging.*.postback'         => ['sometimes', 'array'],
            'entry.*.messaging.*.postback.payload' => ['sometimes', 'string'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'object.in'                            => 'Invalid webhook object type. Expected "page".',
            'entry.required'                       => 'Webhook entry data is required.',
            'entry.*.messaging.required'           => 'Messaging data is required.',
            'entry.*.messaging.*.message.text.max' => 'Message text cannot exceed 2000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'entry.*.messaging.*.sender.id'    => 'sender ID',
            'entry.*.messaging.*.recipient.id' => 'recipient ID',
            'entry.*.messaging.*.message.text' => 'message text',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean and prepare webhook data if needed
    }

    /**
     * Get the processed message data for the service layer
     */
    public function getMessageData(): array
    {
        $entry     = $this->validated()['entry'][0];
        $messaging = $entry['messaging'][0];

        return [
            'sender_id'       => $messaging['sender']['id'],
            'recipient_id'    => $messaging['recipient']['id'],
            'conversation_id' => $messaging['sender']['id'], // Use sender ID as conversation ID
            'content'         => $messaging['message']['text'] ?? $messaging['postback']['payload'] ?? '',
            'timestamp'       => $messaging['timestamp'],
            'message_id'      => $messaging['message']['mid'] ?? null,
            'meta_data'       => [
                'entry_id'      => $entry['id'],
                'entry_time'    => $entry['time'],
                'message_type'  => isset($messaging['message']) ? 'message' : 'postback',
                'raw_messaging' => $messaging,
            ],
        ];
    }

    /**
     * Check if this is a message event (not postback or other event)
     */
    public function isMessage(): bool
    {
        $entry     = $this->validated()['entry'][0] ?? [];
        $messaging = $entry['messaging'][0] ?? [];

        return isset($messaging['message']['text']);
    }

    /**
     * Check if this is a postback event
     */
    public function isPostback(): bool
    {
        $entry     = $this->validated()['entry'][0] ?? [];
        $messaging = $entry['messaging'][0] ?? [];

        return isset($messaging['postback']);
    }
}