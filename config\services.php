<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
     */

    'postmark'  => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend'    => [
        'key' => env('RESEND_KEY'),
    ],

    'ses'       => [
        'key'    => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack'     => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel'              => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'gemini'    => [
        'api_key'         => env('GEMINI_API_KEY'),
        'model'           => env('GEMINI_MODEL', 'gemini-2.0-flash'),
        'embedding_model' => env('GEMINI_EMBEDDING_MODEL', 'text-embedding-004'),
        'max_tokens'      => env('GEMINI_MAX_TOKENS', 1000),
        'temperature'     => env('GEMINI_TEMPERATURE', 0.7),
        'top_p'           => env('GEMINI_TOP_P', 0.9),
        'top_k'           => env('GEMINI_TOP_K', 40),
        'enabled'         => env('GEMINI_ENABLED', true),
    ],

    'ollama'    => [
        'enabled'         => env('OLLAMA_ENABLED', false),
        'api_url'         => env('OLLAMA_API_URL', 'http://localhost:11434'),
        'model'           => env('OLLAMA_MODEL', 'deepseek-r1:8b'),
        'embedding_model' => env('OLLAMA_EMBEDDING_MODEL', 'nomic-embed-text'),
        'max_tokens'      => env('OLLAMA_MAX_TOKENS', 600),
        'temperature'     => env('OLLAMA_TEMPERATURE', 0.7),
        'top_p'           => env('OLLAMA_TOP_P', 0.9),
        'top_k'           => env('OLLAMA_TOP_K', 40),
    ],

    'meta'      => [
        'graph_api_url'        => env('META_GRAPH_API_URL', 'https://graph.instagram.com/v23.0'),
        'webhook_verify_token' => env('META_WEBHOOK_VERIFY_TOKEN'),
        'app_secret'           => env('META_APP_SECRET'),
        'webhook_url'          => env('META_WEBHOOK_URL'),
        'access_token'         => env('META_ACCESS_TOKEN'),
        'page_id'              => env('META_PAGE_ID'),
        'app_id'               => env('META_APP_ID'),
    ],

    'instagram' => [
        'app_id'               => env('INSTAGRAM_APP_ID', '1189767809587929'),
        'app_secret'           => env('INSTAGRAM_APP_SECRET'),
        'redirect_uri'         => env('INSTAGRAM_REDIRECT_URI', 'https://kortana-ai.loca.lt/auth/instagram/callback'),
        'graph_api_url'        => env('INSTAGRAM_GRAPH_API_URL', 'https://graph.instagram.com'),
        'webhook_verify_token' => env('META_WEBHOOK_VERIFY_TOKEN'), // Reuse the same token as Meta
        'client_id'            => env('INSTAGRAM_APP_ID', '1189767809587929'), // Alias for app_id for compatibility
        'client_secret'        => env('INSTAGRAM_APP_SECRET'), // Alias for app_secret for compatibility
    ],

];
