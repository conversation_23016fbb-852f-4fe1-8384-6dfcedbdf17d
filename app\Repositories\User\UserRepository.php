<?php

declare (strict_types = 1);

namespace App\Repositories\User;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserRepository implements UserRepositoryInterface
{
    public function create(array $data)
    {
        return User::create([
            'name'     => $data['name'],
            'email'    => $data['email'],
            'password' => Hash::make($data['password']),
        ]);
    }

    public function findByEmail(string $email)
    {
        return User::where('email', $email)->first();
    }

    public function verifyEmail(int $userId)
    {
        return User::where('id', $userId)->update([
            'email_verified_at' => now(),
        ]);
    }

    public function updatePassword(string $email, string $password)
    {
        return User::where('email', $email)
            ->update(['password' => Hash::make($password)]);
    }

    public function storeResetToken(string $email, string $token)
    {
        return DB::table('password_reset_tokens')->updateOrInsert(
            ['email' => $email],
            [
                'token'      => Hash::make($token),
                'created_at' => now(),
            ]
        );
    }

    public function findResetToken(string $email)
    {
        return DB::table('password_reset_tokens')
            ->where('email', $email)
            ->first();
    }

    public function deleteResetToken(string $email)
    {
        return DB::table('password_reset_tokens')
            ->where('email', $email)
            ->delete();
    }

    public function findById(int $userId)
    {
        return User::find($userId);
    }
}