<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_configs', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->string('category')->index();
            $table->json('value');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['category', 'is_active']);
        });

        // Insert enhanced business configuration with agent and advanced scheduling
        DB::table('business_configs')->insert([
            [
                'key'         => 'agent_profile',
                'category'    => 'agent',
                'value'       => json_encode([
                    'agent_type'    => 'Sales & Marketing',
                    'agent_name'    => 'Kristen',
                    'agent_gender'  => 'female', // male, female, neutral
                    'personality'   => 'friendly', // friendly, professional, casual, formal
                    'shop_uid'      => 'FRAME-' . strtoupper(substr(md5(uniqid()), 0, 8)),
                    'shop_type'     => 'Car wrap & painting',
                    'response_tone' => 'enthusiastic', // enthusiastic, helpful, professional
                ]),
                'description' => 'AI agent profile and personality configuration',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
            [
                'key'         => 'integration_channels',
                'category'    => 'integrations',
                'value'       => json_encode([
                    'instagram' => [
                        'enabled'     => true,
                        'webhook_url' => '/api/instagram/webhook',
                        'priority'    => 1,
                    ],
                    'facebook'  => [
                        'enabled'     => false,
                        'webhook_url' => '/api/facebook/webhook',
                        'priority'    => 2,
                    ],
                    'whatsapp'  => [
                        'enabled'     => false,
                        'webhook_url' => '/api/whatsapp/webhook',
                        'priority'    => 3,
                    ],
                    'twitter'   => [
                        'enabled'     => false,
                        'webhook_url' => '/api/twitter/webhook',
                        'priority'    => 4,
                    ],
                ]),
                'description' => 'Social media integration channels configuration',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
            [
                'key'         => 'business_hours',
                'category'    => 'scheduling',
                'value'       => json_encode([
                    'monday'    => ['09:00', '18:00'],
                    'tuesday'   => ['09:00', '18:00'],
                    'wednesday' => ['09:00', '18:00'],
                    'thursday'  => ['09:00', '18:00'],
                    'friday'    => ['09:00', '18:00'],
                    'saturday'  => ['09:00', '16:00'],
                    'sunday'    => null, // Closed
                ]),
                'description' => 'Business operating hours for each day of the week',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
            [
                'key'         => 'time_slots_config',
                'category'    => 'scheduling',
                'value'       => json_encode([
                    'global_time_periods'   => [
                        'morning'   => ['09:00', '12:00'],
                        'afternoon' => ['13:00', '17:00'],
                        'evening'   => ['17:00', '20:00'],
                    ],
                    'slot_duration_minutes' => 60,
                    'buffer_minutes'        => 15,
                    'max_advance_days'      => 30,
                    'min_advance_hours'     => 2,
                    'slots_per_period'      => [
                        'morning'   => 3, // 3 slots in morning period
                        'afternoon' => 4, // 4 slots in afternoon period
                        'evening'   => 3, // 3 slots in evening period
                    ],
                ]),
                'description' => 'Global time slot configuration and periods',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
            [
                'key'         => 'weekend_settings',
                'category'    => 'scheduling',
                'value'       => json_encode([
                    'saturday_enabled'           => true,
                    'sunday_enabled'             => false,
                    'weekend_hours'              => [
                        'saturday' => ['09:00', '16:00'],
                        'sunday'   => null,
                    ],
                    'weekend_pricing_multiplier' => 1.2, // 20% extra for weekends
                ]),
                'description' => 'Weekend availability and pricing settings',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
            [
                'key'         => 'unavailable_dates',
                'category'    => 'scheduling',
                'value'       => json_encode([
                    'holidays'         => [
                        '2025-12-25', // Christmas
                        '2025-01-01', // New Year
                        '2025-07-04', // Independence Day
                        '2025-11-28', // Thanksgiving
                    ],
                    'vacation_periods' => [
                        ['start' => '2025-08-15', 'end' => '2025-08-22'], // Summer vacation
                        ['start' => '2025-12-23', 'end' => '2025-12-31'], // Holiday break
                    ],
                    'maintenance_days' => [
                        '2025-06-15', // Equipment maintenance
                        '2025-09-10', // Shop renovation
                    ],
                ]),
                'description' => 'Dates when business is unavailable for appointments',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
            [
                'key'         => 'disabled_time_slots',
                'category'    => 'scheduling',
                'value'       => json_encode([
                    '2025-03-15' => ['10:00', '14:00'], // Specific date with disabled times
                    '2025-04-20' => ['09:00', '11:00', '15:00'], // Multiple disabled slots
                    '2025-05-10' => ['13:00'], // Single disabled slot
                ]),
                'description' => 'Specific date and time slot combinations that are disabled',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
            [
                'key'         => 'services',
                'category'    => 'business',
                'value'       => json_encode([
                    'car_wrap_full'       => [
                        'name'        => 'Full Car Wrap',
                        'duration'    => 480, // 8 hours
                        'price'       => 2500.00,
                        'description' => 'Complete vehicle wrap installation',
                        'category'    => 'wrapping',
                    ],
                    'car_wrap_partial'    => [
                        'name'        => 'Partial Car Wrap',
                        'duration'    => 240, // 4 hours
                        'price'       => 1200.00,
                        'description' => 'Partial vehicle wrap (hood, roof, etc.)',
                        'category'    => 'wrapping',
                    ],
                    'paint_job_full'      => [
                        'name'        => 'Full Paint Job',
                        'duration'    => 720, // 12 hours (multi-day)
                        'price'       => 3500.00,
                        'description' => 'Complete vehicle paint job',
                        'category'    => 'painting',
                    ],
                    'paint_touch_up'      => [
                        'name'        => 'Paint Touch-up',
                        'duration'    => 120, // 2 hours
                        'price'       => 300.00,
                        'description' => 'Minor paint repairs and touch-ups',
                        'category'    => 'painting',
                    ],
                    'design_consultation' => [
                        'name'        => 'Design Consultation',
                        'duration'    => 60, // 1 hour
                        'price'       => 100.00,
                        'description' => 'Custom design consultation and planning',
                        'category'    => 'consultation',
                    ],
                ]),
                'description' => 'Car wrap and painting services with pricing',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
            [
                'key'         => 'business_info',
                'category'    => 'business',
                'value'       => json_encode([
                    'name'         => 'Frame Auto Styling',
                    'type'         => 'automotive', // automotive, service, retail, etc.
                    'description'  => 'Professional car wrap and painting services',
                    'specialties'  => ['car wrapping', 'custom paint jobs', 'vehicle graphics'],
                    'contact'      => [
                        'phone'   => '******-FRAME-1',
                        'email'   => '<EMAIL>',
                        'address' => '123 Auto Center Dr, Car City, State 12345',
                    ],
                    'social_media' => [
                        'instagram' => '@frameautostyling',
                        'facebook'  => 'FrameAutoStyling',
                        'website'   => 'https://frameautostyling.com',
                    ],
                ]),
                'description' => 'Business information and contact details',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
            [
                'key'         => 'chatbot_prompts',
                'category'    => 'ai',
                'value'       => json_encode([
                    'greeting'                 => 'Hey there! 👋 I\'m Kristen from Frame Auto Styling! Ready to transform your ride with some amazing wraps or custom paint? What\'s your vision?',
                    'services_inquiry'         => 'We specialize in full car wraps, partial wraps, custom paint jobs, and design consultations. Which service catches your eye? 🚗✨',
                    'appointment_prompt'       => 'Perfect! I\'d love to get you scheduled. I can check our availability and find the perfect time slot for your project. When works best for you?',
                    'appointment_confirmation' => 'Awesome! I found some great time slots for you. Which one fits your schedule? I\'ll get everything set up! 📅',
                    'business_hours_info'      => 'We\'re open Monday-Friday 9 AM to 6 PM, and Saturday 9 AM to 4 PM. Sunday is our day to recharge! ⚡',
                    'pricing_info'             => 'Our pricing varies by project complexity. Full wraps start at $2,500, partials at $1,200, and consultations are $100. Want a custom quote? 💰',
                    'weekend_info'             => 'Saturday appointments are available with a small weekend premium. Sunday we\'re closed but Monday we\'re back in action! 🌟',
                ]),
                'description' => 'AI chatbot conversation prompts for car wrap & painting business',
                'is_active'   => true,
                'created_at'  => now(),
                'updated_at'  => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_configs');
    }
};