# Instagram Webhook Integration Guide

This guide explains how to set up and use Instagram webhooks with your application.

## Prerequisites

Before setting up Instagram webhooks, ensure you have:

1. A Facebook Developer account with access to the Meta Developer Portal
2. An Instagram Professional Account (Business or Creator)
3. A Facebook Page connected to your Instagram Professional Account
4. A registered Meta app with Instagram Basic Display API set up

## Webhook Setup in Meta Developer Portal

1. Go to your app in the [Meta Developer Portal](https://developers.facebook.com/)
2. Navigate to "Products" and add "Webhooks" if not already added
3. Click on "Configure" next to Webhooks
4. Click "Add Subscriptions" and select "Instagram"
5. Configure the following:
    - **Callback URL**: Your webhook endpoint (e.g., `https://yourdomain.com/api/webhooks/instagram`)
    - **Verify Token**: A secret string you define (use a strong, random string)
    - **Subscription Fields**: Select the events you want to subscribe to:
        - `messages`: When someone sends a message to your business
        - `message_reactions`: When someone reacts to a message
        - `messaging_postbacks`: When someone clicks a button in your message
        - `message_echoes`: When your app sends a message
        - `standby`: When your app is in standby mode
        - `comments`: When someone comments on your content
        - `mentions`: When your business is mentioned
6. Save your configuration

## Environment Configuration

In your `.env` file, add the following:

```
INSTAGRAM_APP_ID=your_app_id
INSTAGRAM_APP_SECRET=your_app_secret
INSTAGRAM_REDIRECT_URI=https://yourdomain.com/auth/instagram/callback
META_WEBHOOK_VERIFY_TOKEN=your_verify_token_from_webhook_setup
```

## Webhook Payload Structure

Instagram webhook payloads follow this structure:

    ```json
    {
        "object": "instagram",
        "entry": [
            {
            "id": "<INSTAGRAM_BUSINESS_ID>",
            "time": **********,
                "messaging": [
                    {
                    "sender": {
                        "id": "<SENDER_ID>"
                    },
                    "recipient": {
                        "id": "<RECIPIENT_ID>"
                    },
                    "timestamp": **********,
                        "message": {
                        "mid": "<MESSAGE_ID>",
                        "text": "Hello, this is a test message"
                        }
                    }
                ]
            }
        ]
    }
    ```

## Identifying Business Accounts in Webhooks

When receiving webhooks from Instagram, you can identify which connected business account the message is intended for by using the following IDs in the payload:

1. `entry[].id`: This is the Instagram Business Account ID of your connected account
2. `entry[].messaging[].recipient.id`: This is also your Instagram Business Account ID when receiving messages

The system handles this identification automatically by:

1. Looking up the Instagram Business ID in the database that matches either:

    - The Instagram Business ID stored during authentication
    - The platform user ID stored during authentication

2. When a match is found, the webhook processing system can associate the incoming message with the correct business account and handle it accordingly.

## Webhook Event Types

The system supports processing the following webhook events:

### Messages

When a user sends a message to your Instagram business account:

```json
{
    "message": {
        "mid": "message_id",
        "text": "Message content"
    }
}
```

### Message Reactions

When a user reacts to a message:

```json
{
    "reaction": {
        "mid": "message_id",
        "action": "react",
        "reaction": "love"
    }
}
```

### Postbacks

When a user clicks a button in your message:

```json
{
    "postback": {
        "mid": "message_id",
        "title": "Button Text",
        "payload": "BUTTON_PAYLOAD"
    }
}
```

### Comments

When a user comments on your content:

```json
{
    "changes": [
        {
            "field": "comments",
            "value": {
                "id": "comment_id",
                "from": {
                    "id": "user_id",
                    "username": "username"
                },
                "text": "Comment text",
                "media": {
                    "id": "media_id",
                    "media_product_type": "feed"
                }
            }
        }
    ]
}
```

## Security Considerations

1. **Signature Validation**: All webhook payloads are signed with your app secret. Always validate the signature using the `X-Hub-Signature-256` header.

2. **Verify Token**: When Instagram verifies your webhook endpoint, it sends a verification request that includes the verify token. Always check that this matches your configured token.

3. **Respond Quickly**: Instagram expects your webhook to respond within a few seconds. Always acknowledge receipt of the webhook with a 200 OK response, even if you haven't fully processed the data.

## Troubleshooting

1. **Webhook Verification Fails**: Check that your verify token matches exactly what you configured in the Meta Developer Portal.

2. **Not Receiving Events**: Ensure your app is set to "Live" mode and has the appropriate permissions.

3. **Missing Messages**: Check your logs for any errors in processing webhook events. Also confirm that your Instagram account is connected properly.

4. **Error 500 Responses**: Check your server logs for exceptions. The most common issues are related to permission problems or incorrect configuration.

## Additional Resources

-   [Official Meta Documentation for Instagram Webhooks](https://developers.facebook.com/docs/instagram-platform/webhooks)
-   [Instagram Messaging API](https://developers.facebook.com/docs/messenger-platform/instagram)
-   [Webhook Security Best Practices](https://developers.facebook.com/docs/graph-api/webhooks/getting-started#security-practices)
