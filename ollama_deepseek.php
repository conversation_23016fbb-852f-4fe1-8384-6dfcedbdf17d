<?php
declare (strict_types = 1);

$apiUrl = 'http://localhost:11434/api/chat';

$data = [
    'model'    => 'deepseek-r1:8b',
    'messages' => [
        [
            'role'    => 'system',
            'content' => 'You are a helpful assistant. Always respond in English only.',
        ],
        [
            'role'    => 'user',
            'content' => 'hi',
        ],
    ],
    'stream'   => false,
    'options'  => [
        'temperature' => 0.7,
    ],
];

$ch = curl_init($apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

$response = curl_exec($ch);

if (curl_errno($ch)) {
    echo 'Error: ' . curl_error($ch) . PHP_EOL;
} else {
    $responseData = json_decode($response, true);
    if (isset($responseData['message']['content'])) {
        // Remove any thinking process enclosed in <think> tags
        $content = $responseData['message']['content'];
        $content = preg_replace('/<think>.*?<\/think>/s', '', $content);
        $content = trim($content);
        echo $content . PHP_EOL;
    } else {
        echo $response . PHP_EOL;
    }
}

curl_close($ch);