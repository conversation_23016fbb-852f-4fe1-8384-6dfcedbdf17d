<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class Message extends Model
{
    use HasFactory;

    protected $fillable = [
        'message_id',
        'sender_id',
        'recipient_id',
        'conversation_id',
        'message', // Changed from 'content' to 'message' to match DB schema
        'status',
        'sent_at',
        'timestamp',
        'reply',
        'meta_data',
        'direction',
        'meta_token_id',
        'in_reply_to',
        'platform',
    ];

    protected $casts = [
        'sent_at'   => 'datetime',
        'timestamp' => 'datetime',
        'meta_data' => 'array',
        'status'    => 'string',
        'direction' => 'string',
    ];

    public function getRouteKeyName(): string
    {
        return 'id';
    }

    /**
     * Get the MetaToken associated with this message
     */
    public function metaToken(): BelongsTo
    {
        return $this->belongsTo(MetaToken::class);
    }

    /**
     * Get the message this message is replying to
     */
    public function parentMessage(): BelongsTo
    {
        return $this->belongsTo(Message::class, 'in_reply_to');
    }

    /**
     * Get replies to this message
     */
    public function replies(): HasMany
    {
        return $this->hasMany(Message::class, 'in_reply_to');
    }

    /**
     * Get messages in the same conversation for context
     */
    public function conversationMessages(): HasMany
    {
        return $this->hasMany(Message::class, 'conversation_id', 'conversation_id')
            ->orderBy('sent_at', 'asc');
    }

    /**
     * Scope to get recent messages for a conversation
     */
    public function scopeForConversation($query, string $conversationId, int $limit = 10)
    {
        return $query->where('conversation_id', $conversationId)
            ->orderBy('sent_at', 'desc')
            ->limit($limit);
    }

    /**
     * Scope to get pending messages
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get incoming messages
     */
    public function scopeIncoming($query)
    {
        return $query->where('direction', 'incoming');
    }

    /**
     * Scope to get outgoing messages
     */
    public function scopeOutgoing($query)
    {
        return $query->where('direction', 'outgoing');
    }

    /**
     * Scope to get messages for a specific platform
     */
    public function scopeForPlatform($query, string $platform)
    {
        return $query->where('platform', $platform);
    }
}