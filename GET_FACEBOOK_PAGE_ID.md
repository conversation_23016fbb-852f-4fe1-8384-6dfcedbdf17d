# How to Get Your Facebook Page ID

## 🎯 What You Need

Your **Facebook Page ID** is the unique identifier for the Facebook page that's connected to your Instagram Business account.

---

## 🔍 Method 1: Using Graph API Explorer (Recommended)

### Step 1: Get Your Access Token First

1. Go to [Graph API Explorer](https://developers.facebook.com/tools/explorer/)
2. Select your app: `kortana01-IG` (****************)
3. Generate access token with `pages_show_list` permission
4. Copy the token

### Step 2: Get All Your Facebook Pages

```bash
curl -X GET "https://graph.facebook.com/v23.0/me/accounts" \
  -d "access_token=YOUR_ACCESS_TOKEN"
```

### Response Will Look Like:

```json
{
    "data": [
        {
            "access_token": "page_specific_token...",
            "category": "Business",
            "name": "Your Business Page Name",
            "id": "***************",
            "tasks": ["MANAGE", "CREATE_CONTENT"]
        },
        {
            "access_token": "another_page_token...",
            "category": "Personal",
            "name": "Another Page",
            "id": "***************",
            "tasks": ["MANAGE"]
        }
    ]
}
```

**The `id` field is your Facebook Page ID!**

---

## 🔍 Method 2: From Facebook Page Directly

### Step 1: Go to Your Facebook Page

1. **Open Facebook** in your browser
2. **Go to your business page** (the one connected to Instagram)
3. **Look at the URL** - it will be something like:
    - `https://www.facebook.com/YourPageName` OR
    - `https://www.facebook.com/***************`

### Step 2: Get Page ID from URL

-   **If URL shows page name**: Continue to Method 2B
-   **If URL shows numbers**: Those numbers are your Page ID!

### Step 2B: Get ID from Page Info

1. **On your Facebook page**, click **"About"** tab
2. **Scroll down** to find **"Page Info"** or **"More Info"**
3. **Look for "Page ID"** - it will show the numeric ID

---

## 🔍 Method 3: Using Facebook Page Settings

### Step 1: Go to Page Settings

1. **Go to your Facebook page**
2. **Click "Settings"** (gear icon or in left menu)
3. **Click "Page Info"** in the left sidebar

### Step 2: Find Page ID

-   **Scroll down** to find **"Page ID"**
-   **Copy the numeric ID** (usually 15-16 digits)

---

## 🔍 Method 4: From Your Instagram Business Settings

### Step 1: Check Instagram Connection

1. **Open Instagram app** or web
2. **Go to your business profile**
3. **Settings** → **Account** → **Linked Accounts**
4. **Click "Facebook"**
5. **See connected page name**

### Step 2: Match with Facebook

-   **Note the page name** from Instagram
-   **Use Method 1 or 2** to get the ID for that specific page

---

## ✅ Verify You Have the Correct Page

### Check Instagram Connection:

```bash
# Use your Page ID to verify Instagram connection
curl -X GET "https://graph.facebook.com/v23.0/YOUR_PAGE_ID" \
  -d "fields=instagram_business_account,name" \
  -d "access_token=YOUR_ACCESS_TOKEN"
```

### Expected Response:

```json
{
    "instagram_business_account": {
        "id": "*****************"
    },
    "name": "Your Business Page Name",
    "id": "***************"
}
```

**If you see `instagram_business_account` in the response, you have the correct Page ID!**

---

## 🚨 Troubleshooting

### If No Instagram Business Account Shows:

1. **Check if your Instagram is Business account** (not Personal)
2. **Verify Instagram is connected** to the Facebook page
3. **Make sure you're the admin** of both accounts

### If Multiple Pages Show:

-   **Look for the page name** that matches your Instagram business
-   **Check which page has** `instagram_business_account` field
-   **Use the ID from the page** connected to Instagram

### If Access Denied:

-   **Make sure you have** `pages_show_list` permission
-   **Check if you're admin** of the Facebook page
-   **Verify your access token** is valid

---

## 📋 Quick Commands to Get Everything

### 1. Get All Your Pages:

```bash
curl -X GET "https://graph.facebook.com/v23.0/me/accounts" \
  -d "access_token=YOUR_ACCESS_TOKEN"
```

### 2. Check Specific Page for Instagram:

```bash
curl -X GET "https://graph.facebook.com/v23.0/PAGE_ID" \
  -d "fields=instagram_business_account,name,category" \
  -d "access_token=YOUR_ACCESS_TOKEN"
```

### 3. Get Instagram Business Account Details:

```bash
curl -X GET "https://graph.facebook.com/v23.0/INSTAGRAM_BUSINESS_ID" \
  -d "fields=id,username,name" \
  -d "access_token=YOUR_ACCESS_TOKEN"
```

---

## 🎯 Example Output

**When you run the first command, you'll see something like:**

```json
{
    "data": [
        {
            "access_token": "EAAS...page_token...",
            "category": "Business",
            "name": "Kortana AI Business",
            "id": "***************",
            "tasks": ["MANAGE", "CREATE_CONTENT", "MODERATE"]
        }
    ]
}
```

**Your Page ID is: `***************`**

**Add to your .env:**

```env
INSTAGRAM_PAGE_ID=***************
```

---

## ✅ Final Verification

Once you have your Page ID, verify everything works:

```bash
# Test 1: Check page exists
curl -X GET "https://graph.facebook.com/v23.0/***************" \
  -d "access_token=YOUR_ACCESS_TOKEN"

# Test 2: Check Instagram connection
curl -X GET "https://graph.facebook.com/v23.0/***************" \
  -d "fields=instagram_business_account" \
  -d "access_token=YOUR_ACCESS_TOKEN"

# Test 3: Get Instagram details
curl -X GET "https://graph.facebook.com/v23.0/INSTAGRAM_BUSINESS_ID" \
  -d "fields=username" \
  -d "access_token=YOUR_ACCESS_TOKEN"
```

**If all three commands work, you have the correct Page ID!**
